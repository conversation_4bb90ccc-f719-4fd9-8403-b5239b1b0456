(ns instagram-reels-parser.gui
  "GUI implementation using Seesaw"
  (:require [seesaw.core :as seesaw]
            [seesaw.chooser :as chooser]
            [seesaw.mig :as mig]
            [clojure.tools.logging :as log]
            [clojure.core.async :as async]
            [instagram-reels-parser.config :as config]
            [instagram-reels-parser.parser :as parser]
            [instagram-reels-parser.utils :as utils]
            [clj-time.core :as time]
            [clj-time.format :as time-format])
  (:import [javax.swing JFrame JProgressBar JTextArea JScrollPane SwingUtilities]
           [java.awt.event ActionListener]
           [java.io File]))

(def ^:dynamic *gui-state* (atom {:frame nil
                                  :components {}
                                  :parsing-future nil
                                  :last-file-path nil}))

(defn log-to-gui
  "Add message to GUI log area"
  [message]
  (let [log-area (get-in @*gui-state* [:components :log-area])
        timestamp (time-format/unparse
                   (time-format/formatter "HH:mm:ss")
                   (time/now))
        formatted-message (str "[" timestamp "] " message "\n")]
    (when log-area
      (SwingUtilities/invokeLater
       #(do
          (.append log-area formatted-message)
          (.setCaretPosition log-area (.length (.getText log-area))))))))

(defn update-progress
  "Update progress bar"
  [value]
  (let [progress-bar (get-in @*gui-state* [:components :progress-bar])]
    (when progress-bar
      (SwingUtilities/invokeLater
       #(.setValue progress-bar (int value))))))

(defn update-status
  "Update status label"
  [message]
  (let [status-label (get-in @*gui-state* [:components :status-label])]
    (when status-label
      (SwingUtilities/invokeLater
       #(seesaw/config! status-label :text message)))))

(defn set-parsing-controls-state
  "Enable/disable parsing controls"
  [parsing?]
  (let [parse-button (get-in @*gui-state* [:components :parse-button])
        stop-button (get-in @*gui-state* [:components :stop-button])
        file-button (get-in @*gui-state* [:components :file-button])]
    (SwingUtilities/invokeLater
     #(do
        (when parse-button
          (seesaw/config! parse-button :enabled? (not parsing?)))
        (when stop-button
          (seesaw/config! stop-button :enabled? parsing?))
        (when file-button
          (seesaw/config! file-button :enabled? (not parsing?)))))))

(defn validate-and-update-file
  "Validate selected file and update UI"
  [file-path]
  (if (and file-path (utils/file-exists? file-path))
    (let [validation-result (parser/validate-excel-file file-path)]
      (if (:valid validation-result)
        (do
          (log-to-gui (str "✅ " (:message validation-result)))
          (update-status "File ready for processing")
          (config/set-config! [:gui :last-file] file-path)
          (swap! *gui-state* assoc :last-file-path file-path))
        (do
          (log-to-gui (str "❌ " (:message validation-result)))
          (update-status "File validation error"))))
    (do
      (log-to-gui "❌ File not found")
      (update-status "File not found"))))

(defn select-file
  "Open file chooser dialog"
  []
  (let [current-file (:last-file-path @*gui-state*)
        initial-dir (when current-file (.getParent (File. current-file)))
        file (chooser/choose-file
              :type :open
              :selection-mode :files-only
              :filters [["Excel files" ["xlsx" "xls"]]]
              :dir initial-dir)]
    (when file
      (let [file-path (.getAbsolutePath file)
            file-field (get-in @*gui-state* [:components :file-field])]
        (seesaw/config! file-field :text file-path)
        (log-to-gui (str "📁 Selected file: " file-path))
        (validate-and-update-file file-path)))))

(defn start-parsing
  "Start parsing process"
  []
  (let [file-field (get-in @*gui-state* [:components :file-field])
        file-path (seesaw/config file-field :text)]
    (if (and file-path (not (clojure.string/blank? file-path)))
      (do
        (set-parsing-controls-state true)
        (update-progress 0)
        (update-status "Parsing in progress...")
        (log-to-gui "🚀 Starting parsing process...")
        
        ;; Start parsing in background
        (let [result-chan (parser/parse-async file-path
                                              :progress-callback update-progress
                                              :log-callback log-to-gui)]
          (async/go
            (let [result (async/<! result-chan)]
              (SwingUtilities/invokeLater
               #(do
                  (set-parsing-controls-state false)
                  (if (:success result)
                    (do
                      (update-progress 100)
                      (update-status "Parsing completed successfully"))
                    (update-status "Parsing completed with errors"))))))))
      (do
        (seesaw/alert "Please select an Excel file first")
        (log-to-gui "❌ No file selected")))))

(defn stop-parsing
  "Stop parsing process"
  []
  (parser/stop-parsing!)
  (log-to-gui "🛑 Stop request sent...")
  (update-status "Stopping parsing..."))

(defn open-settings
  "Open settings dialog"
  []
  (let [settings-frame (seesaw/frame
                        :title "Settings"
                        :size [500 :by 400]
                        :resizable? false)
        
        ;; API settings
        api-key-field (seesaw/text :text (config/get-config [:api :access-key] "")
                                   :columns 40)
        base-url-field (seesaw/text :text (config/get-config [:api :base-url] "")
                                    :columns 40)
        debug-checkbox (seesaw/checkbox :text "Debug mode"
                                        :selected? (config/get-config [:api :debug] false))
        
        ;; Settings
        daily-time-field (seesaw/text :text (config/get-config [:settings :daily-run-time] "")
                                      :columns 10)
        delay-field (seesaw/text :text (str (config/get-config [:settings :request-delay] 2))
                                 :columns 10)
        retries-field (seesaw/text :text (str (config/get-config [:settings :max-retries] 3))
                                   :columns 10)
        
        ;; Buttons
        save-button (seesaw/button :text "Save"
                                   :listen [:action
                                            (fn [_]
                                              (try
                                                (config/set-config! [:api :access-key] (seesaw/config api-key-field :text))
                                                (config/set-config! [:api :base-url] (seesaw/config base-url-field :text))
                                                (config/set-config! [:api :debug] (seesaw/config debug-checkbox :selected?))
                                                (config/set-config! [:settings :daily-run-time] (seesaw/config daily-time-field :text))
                                                (config/set-config! [:settings :request-delay] (utils/safe-parse-int (seesaw/config delay-field :text) 2))
                                                (config/set-config! [:settings :max-retries] (utils/safe-parse-int (seesaw/config retries-field :text) 3))
                                                (config/save-config! "config.ini")
                                                (log-to-gui "✅ Settings saved successfully")
                                                (seesaw/dispose! settings-frame)
                                                (catch Exception e
                                                  (log-to-gui (str "❌ Error saving settings: " (.getMessage e)))
                                                  (seesaw/alert (str "Error saving settings: " (.getMessage e))))))])
        cancel-button (seesaw/button :text "Cancel"
                                     :listen [:action (fn [_] (seesaw/dispose! settings-frame))])
        
        content (mig/mig-panel
                 :constraints ["wrap 2", "[right]rel[grow,fill]", ""]
                 :items [["API Key:" ""] [api-key-field "growx"]
                         ["Base URL:" ""] [base-url-field "growx"]
                         ["" ""] [debug-checkbox ""]
                         ["Daily run time:" ""] [daily-time-field ""]
                         ["Request delay (sec):" ""] [delay-field ""]
                         ["Max retries:" ""] [retries-field ""]
                         [save-button "tag ok, span, split 2, sizegroup bttn"]
                         [cancel-button "tag cancel, sizegroup bttn"]])]
    
    (seesaw/config! settings-frame :content content)
    (seesaw/show! settings-frame)))

(defn create-main-window
  "Create main application window"
  []
  (let [;; Components
        file-field (seesaw/text :text (config/get-config [:gui :last-file] "")
                                :columns 50
                                :editable? false)
        file-button (seesaw/button :text "Select File"
                                   :listen [:action (fn [_] (select-file))])
        
        parse-button (seesaw/button :text "Start Parsing"
                                    :listen [:action (fn [_] (start-parsing))])
        stop-button (seesaw/button :text "Stop"
                                   :enabled? false
                                   :listen [:action (fn [_] (stop-parsing))])
        settings-button (seesaw/button :text "Settings"
                                       :listen [:action (fn [_] (open-settings))])
        
        progress-bar (seesaw/progress-bar :min 0 :max 100 :value 0)
        
        log-area (seesaw/text :multi-line? true
                              :editable? false
                              :rows 15
                              :font "Monospaced-PLAIN-12")
        log-scroll (seesaw/scrollable log-area)
        
        status-label (seesaw/label :text "Ready to work")
        
        ;; Layout
        content (mig/mig-panel
                 :constraints ["fill", "[grow,fill]", "[][][grow,fill][]"]
                 :items [[(seesaw/label :text "Instagram Reels Parser v1.0"
                                        :font "Arial-BOLD-16") "span, center, wrap 20px"]
                         
                         [(seesaw/label :text "Excel file with links:") ""]
                         [file-field "growx"]
                         [file-button "wrap 10px"]
                         
                         [parse-button "split 3, sizegroup bttn"]
                         [stop-button "sizegroup bttn"]
                         [settings-button "sizegroup bttn, wrap 10px"]
                         
                         [progress-bar "span, growx, wrap 10px"]
                         
                         [(seesaw/border-panel
                           :north (seesaw/label :text "Execution Log")
                           :center log-scroll) "span, grow, wrap 10px"]
                         
                         [status-label "span, growx"]])
        
        frame (seesaw/frame
               :title "Instagram Reels Parser v1.0"
               :content content
               :size [800 :by 600]
               :on-close :exit)]
    
    ;; Store components for later access
    (swap! *gui-state* assoc
           :frame frame
           :components {:file-field file-field
                        :file-button file-button
                        :parse-button parse-button
                        :stop-button stop-button
                        :progress-bar progress-bar
                        :log-area log-area
                        :status-label status-label}
           :last-file-path (config/get-config [:gui :last-file] ""))
    
    ;; Initial messages
    (log-to-gui "🚀 Instagram Reels Parser started")
    (log-to-gui "📋 Select Excel file with 'Ссылка на Reels' column to begin")
    (log-to-gui "⚙️ Don't forget to configure API key in Settings")
    
    ;; Validate last file if exists
    (let [last-file (config/get-config [:gui :last-file] "")]
      (when (and last-file (utils/file-exists? last-file))
        (log-to-gui (str "📁 Loaded last opened file: " last-file))
        (validate-and-update-file last-file)))
    
    frame))

(defn start-gui
  "Start the GUI application"
  [options]
  (SwingUtilities/invokeLater
   #(let [frame (create-main-window)]
      (seesaw/show! frame))))
