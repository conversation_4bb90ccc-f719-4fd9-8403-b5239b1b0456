# Установка Leiningen для Windows

Leiningen - это инструмент сборки для Clojure проектов. Он необходим для компиляции Instagram Reels Parser.

## Автоматическая установка (рекомендуется)

### Шаг 1: Скачивание
1. Перейдите на https://leiningen.org/
2. Скачайте файл `lein.bat` для Windows
3. Или используйте прямую ссылку: https://raw.githubusercontent.com/technomancy/leiningen/stable/bin/lein.bat

### Шаг 2: Установка
1. Создайте папку `C:\leiningen` (или любую другую)
2. Поместите скачанный `lein.bat` в эту папку
3. Добавьте `C:\leiningen` в переменную PATH:
   - Откройте "Система" → "Дополнительные параметры системы"
   - Нажмите "Переменные среды"
   - В "Системные переменные" найдите "Path" и нажмите "Изменить"
   - Добавьте новый путь: `C:\leiningen`
   - Нажмите "ОК" во всех окнах

### Шаг 3: Завершение установки
1. Откройте новую командную строку (cmd)
2. Выполните команду:
   ```cmd
   lein self-install
   ```
3. Дождитесь завершения загрузки (может занять несколько минут)

### Шаг 4: Проверка
```cmd
lein version
```
Должно показать версию Leiningen (например, "Leiningen 2.10.0 on Java 21.0.3").

## Альтернативные способы установки

### Через Chocolatey (если установлен)
```cmd
choco install lein
```

### Через Scoop (если установлен)
```cmd
scoop install leiningen
```

### Ручная установка JAR файла
Если автоматическая установка не работает:

1. Скачайте `leiningen-X.X.X-standalone.jar` с https://github.com/technomancy/leiningen/releases
2. Поместите в папку `C:\leiningen`
3. Создайте файл `lein.bat` со следующим содержимым:
   ```batch
   @echo off
   java -jar "C:\leiningen\leiningen-X.X.X-standalone.jar" %*
   ```
4. Добавьте `C:\leiningen` в PATH

## Устранение проблем

### Проблема: "Java не найдена"
**Решение**: Убедитесь, что Java 11+ установлена и добавлена в PATH

### Проблема: "Не удается скачать зависимости"
**Решение**: 
- Проверьте подключение к интернету
- Попробуйте запустить из-под администратора
- Проверьте настройки прокси/брандмауэра

### Проблема: "lein не распознается как команда"
**Решение**: 
- Убедитесь, что путь к lein.bat добавлен в PATH
- Перезапустите командную строку
- Проверьте правильность пути

### Проблема: "Ошибка при выполнении lein self-install"
**Решение**:
- Запустите командную строку от имени администратора
- Проверьте права доступа к папке установки
- Попробуйте другую папку для установки

## Проверка установки

После успешной установки выполните:

```cmd
cd path\to\Instagram_Reels_Parser_v1.0
lein deps
```

Если команда выполняется без ошибок и скачивает зависимости, установка прошла успешно.

## Следующие шаги

После установки Leiningen:

1. Перейдите в папку проекта
2. Запустите `build.bat` для сборки проекта
3. Используйте `run.bat` для запуска приложения

## Дополнительная информация

- Официальная документация: https://leiningen.org/
- GitHub репозиторий: https://github.com/technomancy/leiningen
- Руководство пользователя: https://github.com/technomancy/leiningen/blob/stable/doc/TUTORIAL.md
