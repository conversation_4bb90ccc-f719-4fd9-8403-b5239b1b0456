(ns instagram-reels-parser.utils
  "Utility functions for URL parsing and data processing"
  (:require [clojure.string :as str]
            [clojure.tools.logging :as log])
  (:import [java.util.regex Pattern]))

(def instagram-url-patterns
  "Regex patterns for extracting shortcode from Instagram URLs"
  [#"reel/([A-Za-z0-9_-]+)"
   #"reels/([A-Za-z0-9_-]+)"
   #"p/([A-Za-z0-9_-]+)"])

(defn extract-shortcode
  "Extract shortcode from Instagram Reel URL"
  [url]
  (when (and url (string? url))
    (let [url-str (str/trim url)]
      (when-not (str/blank? url-str)
        (loop [patterns instagram-url-patterns]
          (when (seq patterns)
            (let [pattern (first patterns)
                  matcher (re-find pattern url-str)]
              (if matcher
                (second matcher)  ; Return the captured group
                (recur (rest patterns))))))))))

(defn validate-shortcode
  "Validate that shortcode matches Instagram format"
  [shortcode]
  (and shortcode
       (string? shortcode)
       (re-matches #"[A-Za-z0-9_-]+" shortcode)
       (>= (count shortcode) 8)
       (<= (count shortcode) 15)))

(defn validate-instagram-url
  "Validate Instagram URL and extract shortcode"
  [url]
  (when-let [shortcode (extract-shortcode url)]
    (when (validate-shortcode shortcode)
      shortcode)))

(defn format-number
  "Format number with thousands separators"
  [n]
  (if (number? n)
    (let [formatter (java.text.NumberFormat/getInstance)]
      (.format formatter n))
    (str n)))

(defn safe-parse-int
  "Safely parse string to integer, return default on error"
  ([s] (safe-parse-int s 0))
  ([s default]
   (try
     (if (string? s)
       (Integer/parseInt (str/trim s))
       (int s))
     (catch Exception _
       default))))

(defn safe-parse-double
  "Safely parse string to double, return default on error"
  ([s] (safe-parse-double s 0.0))
  ([s default]
   (try
     (if (string? s)
       (Double/parseDouble (str/trim s))
       (double s))
     (catch Exception _
       default))))

(defn current-day
  "Get current day of month as string"
  []
  (str (.getDayOfMonth (java.time.LocalDate/now))))

(defn current-timestamp
  "Get current timestamp in format yyyy-MM-dd HH:mm:ss"
  []
  (let [formatter (java.time.format.DateTimeFormatter/ofPattern "yyyy-MM-dd HH:mm:ss")]
    (.format (java.time.LocalDateTime/now) formatter)))

(defn sleep-seconds
  "Sleep for specified number of seconds"
  [seconds]
  (Thread/sleep (* seconds 1000)))

(defn create-backup-filename
  "Create backup filename with timestamp"
  [original-path]
  (let [file (java.io.File. original-path)
        parent (.getParent file)
        name (.getName file)
        name-without-ext (if (str/includes? name ".")
                           (str/join "." (butlast (str/split name #"\.")))
                           name)
        ext (if (str/includes? name ".")
              (str "." (last (str/split name #"\.")))
              "")
        timestamp (.format (java.time.LocalDateTime/now)
                          (java.time.format.DateTimeFormatter/ofPattern "yyyyMMdd_HHmmss"))
        backup-name (str name-without-ext "_backup_" timestamp ext)]
    (if parent
      (str parent java.io.File/separator backup-name)
      backup-name)))

(defn file-exists?
  "Check if file exists"
  [path]
  (when path
    (.exists (java.io.File. path))))

(defn ensure-directory
  "Ensure directory exists, create if necessary"
  [path]
  (let [dir (java.io.File. path)]
    (when-not (.exists dir)
      (.mkdirs dir))))

(defn copy-file
  "Copy file from source to destination"
  [src dest]
  (try
    (java.nio.file.Files/copy
     (.toPath (java.io.File. src))
     (.toPath (java.io.File. dest))
     (into-array java.nio.file.CopyOption [java.nio.file.StandardCopyOption/REPLACE_EXISTING]))
    true
    (catch Exception e
      (log/error e "Failed to copy file from" src "to" dest)
      false)))

(defn delete-file
  "Delete file safely"
  [path]
  (try
    (.delete (java.io.File. path))
    (catch Exception e
      (log/error e "Failed to delete file:" path)
      false)))

(defn rename-file
  "Rename file safely"
  [old-path new-path]
  (try
    (.renameTo (java.io.File. old-path) (java.io.File. new-path))
    (catch Exception e
      (log/error e "Failed to rename file from" old-path "to" new-path)
      false)))

(defn sanitize-filename
  "Sanitize filename by removing invalid characters"
  [filename]
  (when filename
    (-> filename
        (str/replace #"[<>:\"/\\|?*]" "_")
        (str/replace #"\s+" "_")
        str/trim)))

(defn truncate-string
  "Truncate string to specified length with ellipsis"
  ([s max-length] (truncate-string s max-length "..."))
  ([s max-length suffix]
   (if (and s (> (count s) max-length))
     (str (subs s 0 (- max-length (count suffix))) suffix)
     s)))
