(ns instagram-reels-parser.excel
  "Excel file operations using Apache POI"
  (:require [clojure.tools.logging :as log]
            [instagram-reels-parser.utils :as utils])
  (:import [org.apache.poi.ss.usermodel WorkbookFactory CellType DataFormatter]
           [org.apache.poi.xssf.usermodel XSSFWorkbook]
           [java.io FileInputStream FileOutputStream File]))

(defn cell-value
  "Extract value from Excel cell"
  [cell]
  (when cell
    (let [formatter (DataFormatter.)]
      (case (.getCellType cell)
        CellType/STRING (.getStringCellValue cell)
        CellType/NUMERIC (if (.isCellDateFormatted cell)
                           (.getDateCellValue cell)
                           (.getNumericCellValue cell))
        CellType/BOOLEAN (.getBooleanCellValue cell)
        CellType/FORMULA (.formatCellValue formatter cell)
        CellType/BLANK ""
        ""))))

(defn set-cell-value!
  "Set value to Excel cell"
  [cell value]
  (when cell
    (cond
      (string? value) (.setCellValue cell value)
      (number? value) (.setCellValue cell (double value))
      (boolean? value) (.setCellValue cell value)
      :else (.setCellValue cell (str value)))))

(defn read-excel-file
  "Read Excel file and return data as vector of maps"
  [file-path]
  (try
    (with-open [input-stream (FileInputStream. file-path)]
      (let [workbook (WorkbookFactory/create input-stream)
            sheet (.getSheetAt workbook 0)
            header-row (.getRow sheet 0)]
        
        (when-not header-row
          (throw (ex-info "Excel file is empty or has no header row" {:file file-path})))
        
        ;; Extract headers
        (let [headers (vec (for [i (range (.getLastCellNum header-row))]
                             (let [cell (.getCell header-row i)]
                               (if cell
                                 (str (cell-value cell))
                                 (str "Column_" i)))))
              
              ;; Extract data rows
              data (vec (for [row-num (range 1 (inc (.getLastRowNum sheet)))
                              :let [row (.getRow sheet row-num)]
                              :when row]
                          (into {} (for [i (range (count headers))
                                         :let [cell (.getCell row i)
                                               header (nth headers i)
                                               value (cell-value cell)]]
                                     [header value]))))]
          
          (log/info "Read Excel file:" file-path "with" (count data) "rows and" (count headers) "columns")
          {:headers headers
           :data data
           :row-count (count data)
           :column-count (count headers)})))
    
    (catch Exception e
      (log/error e "Failed to read Excel file:" file-path)
      (throw (ex-info "Failed to read Excel file" {:file file-path :error (.getMessage e)} e)))))

(defn write-excel-file
  "Write data to Excel file"
  [file-path data headers]
  (try
    (let [workbook (XSSFWorkbook.)
          sheet (.createSheet workbook "Sheet1")]
      
      ;; Create header row
      (let [header-row (.createRow sheet 0)]
        (doseq [[i header] (map-indexed vector headers)]
          (let [cell (.createCell header-row i)]
            (.setCellValue cell header))))
      
      ;; Create data rows
      (doseq [[row-idx row-data] (map-indexed vector data)]
        (let [row (.createRow sheet (inc row-idx))]
          (doseq [[col-idx header] (map-indexed vector headers)]
            (let [cell (.createCell row col-idx)
                  value (get row-data header "")]
              (set-cell-value! cell value)))))
      
      ;; Auto-size columns
      (doseq [i (range (count headers))]
        (.autoSizeColumn sheet i))
      
      ;; Write to file
      (with-open [output-stream (FileOutputStream. file-path)]
        (.write workbook output-stream))
      
      (.close workbook)
      (log/info "Wrote Excel file:" file-path "with" (count data) "rows")
      true)
    
    (catch Exception e
      (log/error e "Failed to write Excel file:" file-path)
      false)))

(defn update-excel-file
  "Update existing Excel file with new data"
  [file-path data-updates]
  (try
    (let [temp-file (str file-path ".tmp")]
      (with-open [input-stream (FileInputStream. file-path)]
        (let [workbook (WorkbookFactory/create input-stream)
              sheet (.getSheetAt workbook 0)]
          
          ;; Apply updates
          (doseq [{:keys [row-index column-name value]} data-updates]
            (let [header-row (.getRow sheet 0)
                  column-index (loop [i 0]
                                 (when (< i (.getLastCellNum header-row))
                                   (let [cell (.getCell header-row i)
                                         header (when cell (str (cell-value cell)))]
                                     (if (= header column-name)
                                       i
                                       (recur (inc i))))))]
              
              (when column-index
                (let [row (or (.getRow sheet (inc row-index))
                              (.createRow sheet (inc row-index)))
                      cell (or (.getCell row column-index)
                               (.createCell row column-index))]
                  (set-cell-value! cell value)))))
          
          ;; Write to temporary file
          (with-open [output-stream (FileOutputStream. temp-file)]
            (.write workbook output-stream))
          
          (.close workbook)))
      
      ;; Replace original file with updated one
      (when (utils/file-exists? file-path)
        (utils/delete-file file-path))
      (utils/rename-file temp-file file-path)
      
      (log/info "Updated Excel file:" file-path "with" (count data-updates) "changes")
      true)
    
    (catch Exception e
      (log/error e "Failed to update Excel file:" file-path)
      false)))

(defn validate-excel-file
  "Validate Excel file structure"
  [file-path url-column]
  (try
    (when-not (utils/file-exists? file-path)
      (throw (ex-info "File not found" {:file file-path})))
    
    (let [{:keys [headers data]} (read-excel-file file-path)]
      (when-not (some #(= % url-column) headers)
        (throw (ex-info (str "Column '" url-column "' not found")
                        {:file file-path
                         :available-columns headers
                         :required-column url-column})))
      
      (when (empty? data)
        (throw (ex-info "Excel file is empty" {:file file-path})))
      
      ;; Count valid URLs
      (let [valid-urls (count (filter #(utils/validate-instagram-url (get % url-column)) data))]
        (when (zero? valid-urls)
          (throw (ex-info "No valid Instagram Reels URLs found" {:file file-path})))
        
        {:valid true
         :message (str "File is valid. Found " valid-urls " valid URLs out of " (count data))
         :total-rows (count data)
         :valid-urls valid-urls
         :headers headers}))
    
    (catch clojure.lang.ExceptionInfo e
      {:valid false
       :message (.getMessage e)
       :error-data (ex-data e)})
    
    (catch Exception e
      {:valid false
       :message (str "Error reading file: " (.getMessage e))
       :error e})))

(defn get-excel-column-names
  "Get column names from Excel file"
  [file-path]
  (try
    (:headers (read-excel-file file-path))
    (catch Exception e
      (log/error e "Failed to get column names from:" file-path)
      [])))
