# Развертывание Instagram Reels Parser (Clojure)

## Требования к системе

### Обязательные компоненты:
- **Java 11 или выше** - для запуска приложения
- **Leiningen 2.9.0+** - для сборки проекта (только для разработки)

### Проверка установки Java:
```cmd
java -version
```
Должно показать версию 11 или выше.

## Быстрый старт

### 1. Сборка проекта
```cmd
build.bat
```

### 2. Настройка конфигурации
Отредактируйте файл `config.ini`:
```ini
[API]
access_key = YOUR_HIKER_API_KEY_HERE
base_url = https://api.hikerapi.com/v2
debug = false

[SETTINGS]
daily_run_time = 09:00
request_delay = 2
max_retries = 3
```

### 3. Запуск приложения

#### GUI режим (рекомендуется):
```cmd
run.bat
```

#### CLI режим:
```cmd
run_cli.bat data.xlsx
```

#### Планировщик:
```cmd
schedule.bat data.xlsx 09:00
```

## Подробная инструкция по развертыванию

### Шаг 1: Подготовка окружения

1. **Установка Java**:
   - Скачайте OpenJDK 11+ с https://adoptium.net/
   - Установите и добавьте в PATH
   - Проверьте: `java -version`

2. **Установка Leiningen** (только для разработки):
   - Скачайте с https://leiningen.org/
   - Добавьте в PATH
   - Проверьте: `lein version`

### Шаг 2: Получение исходного кода

```cmd
git clone <repository-url>
cd Instagram_Reels_Parser_v1.0
```

### Шаг 3: Сборка приложения

```cmd
# Автоматическая сборка
build.bat

# Или вручную:
lein clean
lein deps
lein test
lein uberjar
```

После успешной сборки будет создан файл:
`target\uberjar\instagram-reels-parser-1.0.0-standalone.jar`

### Шаг 4: Настройка конфигурации

Создайте или отредактируйте файл `config.ini`:

```ini
[API]
# Ваш ключ API от HikerAPI
access_key = YOUR_HIKER_API_KEY_HERE
base_url = https://api.hikerapi.com/v2
debug = false

[SETTINGS]
# Время ежедневного запуска (формат HH:mm)
daily_run_time = 09:00
# Задержка между запросами в секундах
request_delay = 2
# Максимальное количество повторных попыток
max_retries = 3
# Таймаут запроса в секундах
request_timeout = 30

[FILES]
# Название колонки с URL в Excel файле
url_column = Ссылка на Reels
# Название колонки с именем аккаунта
account_column = Account_Name
# Режим сохранения (same_file или new_file)
save_mode = same_file

[GUI]
# Последний открытый файл
last_file = 
```

### Шаг 5: Подготовка Excel файла

Создайте Excel файл со следующими колонками:
- **Ссылка на Reels** - URL Instagram Reels
- **Account_Name** - имя аккаунта (опционально)
- **1, 2, 3, ..., 31** - колонки для данных по дням месяца

Пример структуры:
| Ссылка на Reels | Account_Name | 1 | 2 | 3 | ... | 31 |
|------------------|--------------|---|---|---|-----|----| 
| https://www.instagram.com/reel/ABC123/ | @username | | | | | |

## Режимы запуска

### 1. GUI режим (графический интерфейс)

```cmd
run.bat
```

**Возможности:**
- Выбор Excel файла через диалог
- Настройка параметров через GUI
- Отслеживание прогресса в реальном времени
- Просмотр логов выполнения
- Планирование автоматических запусков

### 2. CLI режим (командная строка)

```cmd
run_cli.bat path\to\your\file.xlsx
```

**Возможности:**
- Автоматическая обработка без GUI
- Подходит для автоматизации
- Вывод прогресса в консоль

### 3. Планировщик

```cmd
schedule.bat path\to\your\file.xlsx 09:00
```

**Возможности:**
- Ежедневный автоматический запуск
- Настраиваемое время выполнения
- Работает в фоновом режиме

## Автоматизация через Windows Task Scheduler

### Создание задачи:

1. Откройте "Планировщик заданий Windows"
2. Создайте новую задачу
3. Настройте триггер (ежедневно в нужное время)
4. Настройте действие:
   - **Программа**: `cmd.exe`
   - **Аргументы**: `/c "cd /d C:\path\to\project && schedule.bat data.xlsx 09:00"`
   - **Рабочая папка**: `C:\path\to\project`

## Устранение неполадок

### Проблема: "Java не найдена"
**Решение**: Установите Java 11+ и добавьте в PATH

### Проблема: "JAR файл не найден"
**Решение**: Запустите `build.bat` для сборки проекта

### Проблема: "Ошибка API ключа"
**Решение**: Проверьте правильность API ключа в `config.ini`

### Проблема: "Файл Excel не найден"
**Решение**: Убедитесь, что путь к файлу указан правильно

### Проблема: "Колонка не найдена"
**Решение**: Проверьте название колонки в настройках `url_column`

## Логирование

Логи сохраняются в файл `instagram_parser.log` с автоматической ротацией:
- Максимальный размер файла: 10MB
- Хранится 30 дней истории
- Общий лимит: 300MB

## Производительность

### Рекомендуемые настройки:
- `request_delay = 2` - для избежания блокировки API
- `max_retries = 3` - для надежности
- `request_timeout = 30` - для стабильности

### Ограничения API:
- HikerAPI имеет лимиты на количество запросов
- При превышении лимитов приложение автоматически ждет

## Обновление

1. Получите новую версию кода
2. Запустите `build.bat`
3. Скопируйте свой `config.ini` если нужно
4. Перезапустите приложение

## Поддержка

При возникновении проблем:
1. Проверьте логи в `instagram_parser.log`
2. Убедитесь в правильности конфигурации
3. Проверьте подключение к интернету
4. Создайте issue в репозитории проекта
