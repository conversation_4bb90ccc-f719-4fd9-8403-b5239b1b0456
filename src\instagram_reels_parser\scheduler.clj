(ns instagram-reels-parser.scheduler
  "Task scheduling and automation"
  (:require [overtone.at-at :as at-at]
            [clj-time.core :as time]
            [clj-time.format :as time-format]
            [clj-time.periodic :as periodic]
            [clojure.tools.logging :as log]
            [instagram-reels-parser.config :as config]
            [instagram-reels-parser.parser :as parser])
  (:import [java.util.concurrent ScheduledThreadPoolExecutor]))

(def ^:dynamic *scheduler-pool* (at-at/mk-pool))
(def ^:dynamic *scheduled-jobs* (atom {}))

(defn parse-time-string
  "Parse time string in HH:mm format"
  [time-str]
  (try
    (let [formatter (time-format/formatter "HH:mm")
          parsed-time (time-format/parse formatter time-str)]
      {:hour (time/hour parsed-time)
       :minute (time/minute parsed-time)})
    (catch Exception e
      (log/error e "Failed to parse time string:" time-str)
      {:hour 9 :minute 0})))

(defn next-scheduled-time
  "Calculate next scheduled time for daily execution"
  [time-str]
  (let [{:keys [hour minute]} (parse-time-string time-str)
        now (time/now)
        today-scheduled (time/date-time (time/year now) (time/month now) (time/day now) hour minute)
        next-time (if (time/after? now today-scheduled)
                    (time/plus today-scheduled (time/days 1))
                    today-scheduled)]
    next-time))

(defn schedule-daily-parsing
  "Schedule daily parsing at specified time"
  [excel-path time-str & {:keys [log-callback]}]
  (try
    (let [next-time (next-scheduled-time time-str)
          delay-ms (- (time/in-millis next-time) (time/in-millis (time/now)))
          
          scheduled-fn (fn []
                         (when log-callback
                           (log-callback (str "🕒 Starting scheduled parsing at " 
                                              (time-format/unparse 
                                               (time-format/formatter "HH:mm:ss") 
                                               (time/now)))))
                         (try
                           (parser/parse-and-save excel-path :log-callback log-callback)
                           (catch Exception e
                             (log/error e "Error in scheduled parsing")
                             (when log-callback
                               (log-callback (str "❌ Scheduled parsing error: " (.getMessage e))))))
                         
                         ;; Schedule next execution
                         (schedule-daily-parsing excel-path time-str :log-callback log-callback))
          
          job (at-at/after delay-ms scheduled-fn *scheduler-pool*)]
      
      ;; Store job reference
      (swap! *scheduled-jobs* assoc :daily-parsing job)
      
      (when log-callback
        (log-callback (str "⏰ Daily parsing scheduled for " 
                           (time-format/unparse 
                            (time-format/formatter "yyyy-MM-dd HH:mm") 
                            next-time))))
      
      (log/info "Daily parsing scheduled for" (str next-time))
      true)
    
    (catch Exception e
      (log/error e "Failed to schedule daily parsing")
      (when log-callback
        (log-callback (str "❌ Scheduling error: " (.getMessage e))))
      false)))

(defn cancel-scheduled-parsing
  "Cancel scheduled daily parsing"
  [& {:keys [log-callback]}]
  (try
    (when-let [job (get @*scheduled-jobs* :daily-parsing)]
      (at-at/kill job)
      (swap! *scheduled-jobs* dissoc :daily-parsing)
      (when log-callback
        (log-callback "✅ Scheduled parsing cancelled"))
      (log/info "Scheduled parsing cancelled")
      true)
    (catch Exception e
      (log/error e "Failed to cancel scheduled parsing")
      (when log-callback
        (log-callback (str "❌ Error cancelling schedule: " (.getMessage e))))
      false)))

(defn is-parsing-scheduled?
  "Check if daily parsing is currently scheduled"
  []
  (contains? @*scheduled-jobs* :daily-parsing))

(defn get-next-scheduled-time
  "Get next scheduled execution time"
  []
  (when (is-parsing-scheduled?)
    (let [time-str (config/get-config [:settings :daily-run-time] "09:00")]
      (next-scheduled-time time-str))))

(defn schedule-one-time-parsing
  "Schedule one-time parsing after specified delay"
  [excel-path delay-seconds & {:keys [log-callback]}]
  (try
    (let [scheduled-fn (fn []
                         (when log-callback
                           (log-callback "🕒 Starting one-time scheduled parsing"))
                         (try
                           (parser/parse-and-save excel-path :log-callback log-callback)
                           (catch Exception e
                             (log/error e "Error in one-time scheduled parsing")
                             (when log-callback
                               (log-callback (str "❌ One-time parsing error: " (.getMessage e)))))))
          
          job (at-at/after (* delay-seconds 1000) scheduled-fn *scheduler-pool*)
          job-id (keyword (str "one-time-" (System/currentTimeMillis)))]
      
      ;; Store job reference
      (swap! *scheduled-jobs* assoc job-id job)
      
      (when log-callback
        (log-callback (str "⏰ One-time parsing scheduled in " delay-seconds " seconds")))
      
      (log/info "One-time parsing scheduled in" delay-seconds "seconds")
      job-id)
    
    (catch Exception e
      (log/error e "Failed to schedule one-time parsing")
      (when log-callback
        (log-callback (str "❌ One-time scheduling error: " (.getMessage e))))
      nil)))

(defn cancel-one-time-parsing
  "Cancel one-time scheduled parsing"
  [job-id & {:keys [log-callback]}]
  (try
    (when-let [job (get @*scheduled-jobs* job-id)]
      (at-at/kill job)
      (swap! *scheduled-jobs* dissoc job-id)
      (when log-callback
        (log-callback "✅ One-time parsing cancelled"))
      (log/info "One-time parsing cancelled")
      true)
    (catch Exception e
      (log/error e "Failed to cancel one-time parsing")
      (when log-callback
        (log-callback (str "❌ Error cancelling one-time schedule: " (.getMessage e))))
      false)))

(defn list-scheduled-jobs
  "List all currently scheduled jobs"
  []
  (keys @*scheduled-jobs*))

(defn cancel-all-jobs
  "Cancel all scheduled jobs"
  [& {:keys [log-callback]}]
  (try
    (doseq [[job-id job] @*scheduled-jobs*]
      (at-at/kill job))
    (reset! *scheduled-jobs* {})
    (when log-callback
      (log-callback "✅ All scheduled jobs cancelled"))
    (log/info "All scheduled jobs cancelled")
    true
    (catch Exception e
      (log/error e "Failed to cancel all jobs")
      (when log-callback
        (log-callback (str "❌ Error cancelling all jobs: " (.getMessage e))))
      false)))

(defn shutdown-scheduler
  "Shutdown the scheduler pool"
  []
  (try
    (cancel-all-jobs)
    (at-at/stop-and-reset-pool! *scheduler-pool*)
    (log/info "Scheduler shutdown completed")
    (catch Exception e
      (log/error e "Error during scheduler shutdown"))))

(defn get-scheduler-status
  "Get current scheduler status"
  []
  {:active-jobs (count @*scheduled-jobs*)
   :jobs (keys @*scheduled-jobs*)
   :daily-parsing-scheduled? (is-parsing-scheduled?)
   :next-scheduled-time (when (is-parsing-scheduled?)
                          (str (get-next-scheduled-time)))
   :pool-size (.getPoolSize *scheduler-pool*)
   :active-count (.getActiveCount *scheduler-pool*)})

;; Shutdown hook to clean up scheduler on JVM exit
(defn add-shutdown-hook
  "Add shutdown hook to clean up scheduler"
  []
  (.addShutdownHook (Runtime/getRuntime)
                    (Thread. shutdown-scheduler)))
