(ns instagram-reels-parser.config-test
  (:require [clojure.test :refer :all]
            [instagram-reels-parser.config :as config]
            [clojure.java.io :as io]))

(def test-config-content
  "[API]
access_key = test_key_123
base_url = https://api.test.com
debug = true

[SETTINGS]
daily_run_time = 10:30
request_delay = 3
max_retries = 5")

(deftest test-parse-ini-content
  (testing "Parse INI content"
    (let [result (config/parse-ini-content test-config-content)]
      (is (= "test_key_123" (get-in result [:api :access-key])))
      (is (= "https://api.test.com" (get-in result [:api :base-url])))
      (is (= true (get-in result [:api :debug])))
      (is (= "10:30" (get-in result [:settings :daily-run-time])))
      (is (= 3 (get-in result [:settings :request-delay])))
      (is (= 5 (get-in result [:settings :max-retries]))))))

(deftest test-get-config
  (testing "Get configuration values"
    (config/reset-config!)
    (config/set-config! [:test :value] "test123")
    (is (= "test123" (config/get-config [:test :value])))
    (is (= "default" (config/get-config [:nonexistent :key] "default")))
    (is (nil? (config/get-config [:nonexistent :key])))))

(deftest test-set-config
  (testing "Set configuration values"
    (config/reset-config!)
    (config/set-config! [:api :key] "new_key")
    (is (= "new_key" (config/get-config [:api :key])))
    
    (config/set-config! [:nested :deep :value] 42)
    (is (= 42 (config/get-config [:nested :deep :value])))))

(deftest test-config-to-ini-string
  (testing "Convert config to INI string"
    (config/reset-config!)
    (config/set-config! [:api :access-key] "test_key")
    (config/set-config! [:api :base-url] "https://test.com")
    (config/set-config! [:settings :delay] 2)
    
    (let [ini-string (config/config-to-ini-string)]
      (is (clojure.string/includes? ini-string "[API]"))
      (is (clojure.string/includes? ini-string "access_key = test_key"))
      (is (clojure.string/includes? ini-string "[SETTINGS]"))
      (is (clojure.string/includes? ini-string "delay = 2")))))
