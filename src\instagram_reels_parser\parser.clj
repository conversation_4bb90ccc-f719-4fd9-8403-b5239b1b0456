(ns instagram-reels-parser.parser
  "Main parsing logic for Instagram Reels"
  (:require [clojure.tools.logging :as log]
            [instagram-reels-parser.config :as config]
            [instagram-reels-parser.api :as api]
            [instagram-reels-parser.excel :as excel]
            [instagram-reels-parser.utils :as utils]
            [clojure.core.async :as async]))

(def ^:dynamic *parsing-state* (atom {:running false
                                      :stats {:success 0 :errors 0 :total 0}
                                      :current-item nil}))

(defn get-parsing-stats
  "Get current parsing statistics"
  []
  (:stats @*parsing-state*))

(defn is-parsing?
  "Check if parsing is currently running"
  []
  (:running @*parsing-state*))

(defn stop-parsing!
  "Stop the parsing process"
  []
  (swap! *parsing-state* assoc :running false)
  (log/info "Parsing stop requested"))

(defn update-stats!
  "Update parsing statistics"
  [stat-type]
  (swap! *parsing-state* update-in [:stats stat-type] inc))

(defn reset-stats!
  "Reset parsing statistics"
  []
  (swap! *parsing-state* assoc :stats {:success 0 :errors 0 :total 0}))

(defn validate-excel-file
  "Validate Excel file for parsing"
  [file-path]
  (let [url-column (config/get-config [:files :url-column] "Ссылка на Reels")]
    (excel/validate-excel-file file-path url-column)))

(defn parse-single-reel
  "Parse a single Instagram Reel"
  [url row-index progress-callback log-callback]
  (try
    (when-let [shortcode (utils/validate-instagram-url url)]
      (when log-callback
        (log-callback (str "[" (inc row-index) "] Parsing: " url)))
      
      (let [data (api/fetch-reel-data shortcode)
            views (:views data 0)]
        
        (if (:error data)
          (do
            (update-stats! :errors)
            (when log-callback
              (log-callback (str "❌ Error: " (:error data))))
            {:success false :views 0 :error (:error data)})
          (do
            (update-stats! :success)
            (when log-callback
              (log-callback (str "✅ Views: " (utils/format-number views))))
            {:success true :views views :data data}))))
    
    (catch Exception e
      (update-stats! :errors)
      (let [error-msg (str "Exception parsing reel: " (.getMessage e))]
        (log/error e error-msg)
        (when log-callback
          (log-callback (str "❌ " error-msg)))
        {:success false :views 0 :error error-msg}))))

(defn parse-and-save
  "Main parsing function with progress tracking"
  [excel-path & {:keys [progress-callback log-callback]}]
  (when (is-parsing?)
    (when log-callback
      (log-callback "Parsing is already running!"))
    (throw (ex-info "Parsing already in progress" {:status :already-running})))
  
  (swap! *parsing-state* assoc :running true)
  (reset-stats!)
  
  (try
    ;; Validate file
    (let [validation-result (validate-excel-file excel-path)]
      (when-not (:valid validation-result)
        (when log-callback
          (log-callback (str "Validation error: " (:message validation-result))))
        (throw (ex-info "File validation failed" validation-result))))
    
    (when log-callback
      (log-callback (str "File validation passed: " (:message (validate-excel-file excel-path)))))
    
    ;; Read Excel file
    (let [{:keys [data headers]} (excel/read-excel-file excel-path)
          url-column (config/get-config [:files :url-column] "Ссылка на Reels")
          request-delay (config/get-config [:settings :request-delay] 2)
          current-day (utils/current-day)
          day-column current-day]
      
      (when log-callback
        (log-callback (str "📅 Today is day " current-day ", will write to column '" day-column "'")))
      
      ;; Find rows with valid URLs
      (let [valid-rows (vec (keep-indexed
                             (fn [idx row]
                               (when (utils/validate-instagram-url (get row url-column))
                                 {:index idx :row row :url (get row url-column)}))
                             data))
            total-count (count valid-rows)]
        
        (swap! *parsing-state* assoc-in [:stats :total] total-count)
        
        (when log-callback
          (log-callback (str "🎯 Found " total-count " valid Reels for parsing")))
        
        ;; Parse each reel
        (let [updates (atom [])]
          (loop [remaining-rows (map-indexed vector valid-rows)]
            (when (and (seq remaining-rows) (is-parsing?))
              (let [[i {:keys [index url]}] (first remaining-rows)]
                (swap! *parsing-state* assoc :current-item {:index i :url url})
            
                (let [result (parse-single-reel url i progress-callback log-callback)
                      views (:views result 0)]

                  ;; Store update for batch processing
                  (swap! updates conj {:row-index index
                                       :column-name day-column
                                       :value views})

                  ;; Update progress
                  (when progress-callback
                    (let [progress (* (/ (inc i) total-count) 100)]
                      (progress-callback progress)))

                  ;; Delay between requests
                  (utils/sleep-seconds request-delay)

                  ;; Continue with remaining rows
                  (recur (rest remaining-rows))))))
          
          ;; Save updates to Excel file
          (when (seq @updates)
            (let [backup-path (utils/create-backup-filename excel-path)]
              (try
                ;; Create backup
                (when (utils/file-exists? excel-path)
                  (utils/copy-file excel-path backup-path))
                
                ;; Apply updates
                (if (excel/update-excel-file excel-path @updates)
                  (do
                    (when log-callback
                      (log-callback "✅ Parsing completed!")
                      (log-callback (str "📊 Statistics: Success: " (:success (get-parsing-stats))
                                         ", Errors: " (:errors (get-parsing-stats))))
                      (log-callback (str "💾 Data updated in file: " excel-path))
                      (log-callback (str "🛡️ Backup created: " backup-path)))
                    (log/info "Parsing completed successfully. Processed:" total-count
                              "Success:" (:success (get-parsing-stats))))
                  (do
                    (when log-callback
                      (log-callback "❌ Failed to save updates to Excel file"))
                    (throw (ex-info "Failed to save Excel file" {:file excel-path}))))
                
                (catch Exception e
                  (log/error e "Error saving Excel file")
                  (when log-callback
                    (log-callback (str "❌ Error saving file: " (.getMessage e))))
                  (throw e))))))
        
        {:success true
         :stats (get-parsing-stats)
         :total-processed total-count}))
    
    (catch clojure.lang.ExceptionInfo e
      (let [error-msg (.getMessage e)]
        (when log-callback
          (log-callback error-msg))
        (log/error e error-msg)
        {:success false :error error-msg :data (ex-data e)}))
    
    (catch Exception e
      (let [error-msg (str "Critical error during parsing: " (.getMessage e))]
        (when log-callback
          (log-callback error-msg))
        (log/error e error-msg)
        {:success false :error error-msg}))
    
    (finally
      (swap! *parsing-state* assoc :running false :current-item nil))))

(defn parse-async
  "Parse asynchronously and return a channel with results"
  [excel-path & {:keys [progress-callback log-callback]}]
  (let [result-chan (async/chan 1)]
    (async/thread
      (try
        (let [result (parse-and-save excel-path
                                     :progress-callback progress-callback
                                     :log-callback log-callback)]
          (async/>!! result-chan result))
        (catch Exception e
          (async/>!! result-chan {:success false :error (.getMessage e)}))
        (finally
          (async/close! result-chan))))
    result-chan))
