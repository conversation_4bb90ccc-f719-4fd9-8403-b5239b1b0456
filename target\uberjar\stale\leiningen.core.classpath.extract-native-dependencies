[{:dependencies {org.clojure/clojure {:vsn "1.11.1", :native-prefix nil}, org.clojure/tools.analyzer {:vsn "1.1.0", :native-prefix nil}, org.apache.poi/poi {:vsn "5.2.4", :native-prefix nil}, org.apache.commons/commons-compress {:vsn "1.24.0", :native-prefix nil}, org.clojure/tools.logging {:vsn "1.2.4", :native-prefix nil}, org.clojure/core.specs.alpha {:vsn "0.2.62", :native-prefix nil}, org.apache.poi/poi-ooxml-schemas {:vsn "4.1.2", :native-prefix nil}, org.clojure/spec.alpha {:vsn "0.3.218", :native-prefix nil}, org.clojure/tools.cli {:vsn "1.0.219", :native-prefix nil}, clj-time {:vsn "0.15.2", :native-prefix nil}, org.swinglabs.swingx/swingx-plaf {:vsn "1.6.3", :native-prefix nil}, org.apache.httpcomponents/httpasyncclient {:vsn "4.1.4", :native-prefix nil}, org.clojure/tools.analyzer.jvm {:vsn "1.2.2", :native-prefix nil}, riddley {:vsn "0.1.12", :native-prefix nil}, org.swinglabs.swingx/swingx-action {:vsn "1.6.3", :native-prefix nil}, org.apache.commons/commons-math3 {:vsn "3.6.1", :native-prefix nil}, com.fasterxml.jackson.dataformat/jackson-dataformat-cbor {:vsn "2.13.3", :native-prefix nil}, ch.qos.logback/logback-core {:vsn "1.4.11", :native-prefix nil}, org.apache.httpcomponents/httpcore-nio {:vsn "4.4.10", :native-prefix nil}, com.fasterxml.jackson.core/jackson-core {:vsn "2.13.3", :native-prefix nil}, slingshot {:vsn "0.12.2", :native-prefix nil}, org.ow2.asm/asm {:vsn "9.2", :native-prefix nil}, cheshire {:vsn "5.11.0", :native-prefix nil}, org.swinglabs.swingx/swingx-painters {:vsn "1.6.3", :native-prefix nil}, org.apache.httpcomponents/httpcore {:vsn "4.4.14", :native-prefix nil}, j18n {:vsn "1.0.2", :native-prefix nil}, org.apache.httpcomponents/httpclient-cache {:vsn "4.5.13", :native-prefix nil}, com.miglayout/miglayout {:vsn "3.7.4", :native-prefix nil}, potemkin {:vsn "0.4.5", :native-prefix nil}, org.apache.poi/poi-ooxml-lite {:vsn "5.2.4", :native-prefix nil}, org.apache.xmlbeans/xmlbeans {:vsn "5.1.1", :native-prefix nil}, org.apache.poi/poi-ooxml {:vsn "5.2.4", :native-prefix nil}, com.fifesoft/rsyntaxtextarea {:vsn "2.5.6", :native-prefix nil}, commons-codec {:vsn "1.15", :native-prefix nil}, commons-logging {:vsn "1.2", :native-prefix nil}, org.clojure/data.csv {:vsn "1.0.1", :native-prefix nil}, org.apache.httpcomponents/httpclient {:vsn "4.5.13", :native-prefix nil}, org.swinglabs.swingx/swingx-core {:vsn "1.6.3", :native-prefix nil}, com.zaxxer/SparseBitSet {:vsn "1.3", :native-prefix nil}, com.jgoodies/forms {:vsn "1.2.1", :native-prefix nil}, org.clojure/tools.reader {:vsn "1.3.6", :native-prefix nil}, overtone/at-at {:vsn "1.2.0", :native-prefix nil}, joda-time {:vsn "2.10", :native-prefix nil}, org.swinglabs.swingx/swingx-common {:vsn "1.6.3", :native-prefix nil}, tigris {:vsn "0.1.2", :native-prefix nil}, clj-http {:vsn "3.12.3", :native-prefix nil}, org.apache.commons/commons-collections4 {:vsn "4.4", :native-prefix nil}, org.slf4j/slf4j-api {:vsn "2.0.7", :native-prefix nil}, org.swinglabs.swingx/swingx-autocomplete {:vsn "1.6.3", :native-prefix nil}, org.apache.logging.log4j/log4j-api {:vsn "2.20.0", :native-prefix nil}, com.github.virtuald/curvesapi {:vsn "1.08", :native-prefix nil}, me.raynes/fs {:vsn "1.4.6", :native-prefix nil}, clj-tuple {:vsn "0.2.2", :native-prefix nil}, ch.qos.logback/logback-classic {:vsn "1.4.11", :native-prefix nil}, org.clojure/core.memoize {:vsn "1.0.253", :native-prefix nil}, org.clojure/data.priority-map {:vsn "1.1.0", :native-prefix nil}, commons-io {:vsn "2.8.0", :native-prefix nil}, org.apache.httpcomponents/httpmime {:vsn "4.5.13", :native-prefix nil}, org.clojure/core.cache {:vsn "1.0.225", :native-prefix nil}, org.clojure/core.async {:vsn "1.6.673", :native-prefix nil}, com.fasterxml.jackson.dataformat/jackson-dataformat-smile {:vsn "2.13.3", :native-prefix nil}, seesaw {:vsn "1.5.0", :native-prefix nil}}, :native-path "target/uberjar/native"} {:native-path "target/uberjar/native", :dependencies {org.clojure/clojure {:vsn "1.11.1", :native-prefix nil, :native? false}, org.clojure/tools.analyzer {:vsn "1.1.0", :native-prefix nil, :native? false}, org.apache.poi/poi {:vsn "5.2.4", :native-prefix nil, :native? false}, org.apache.commons/commons-compress {:vsn "1.24.0", :native-prefix nil, :native? false}, org.clojure/tools.logging {:vsn "1.2.4", :native-prefix nil, :native? false}, org.clojure/core.specs.alpha {:vsn "0.2.62", :native-prefix nil, :native? false}, org.apache.poi/poi-ooxml-schemas {:vsn "4.1.2", :native-prefix nil, :native? false}, org.clojure/spec.alpha {:vsn "0.3.218", :native-prefix nil, :native? false}, org.clojure/tools.cli {:vsn "1.0.219", :native-prefix nil, :native? false}, clj-time {:vsn "0.15.2", :native-prefix nil, :native? false}, org.swinglabs.swingx/swingx-plaf {:vsn "1.6.3", :native-prefix nil, :native? false}, org.apache.httpcomponents/httpasyncclient {:vsn "4.1.4", :native-prefix nil, :native? false}, org.clojure/tools.analyzer.jvm {:vsn "1.2.2", :native-prefix nil, :native? false}, riddley {:vsn "0.1.12", :native-prefix nil, :native? false}, org.swinglabs.swingx/swingx-action {:vsn "1.6.3", :native-prefix nil, :native? false}, org.apache.commons/commons-math3 {:vsn "3.6.1", :native-prefix nil, :native? false}, com.fasterxml.jackson.dataformat/jackson-dataformat-cbor {:vsn "2.13.3", :native-prefix nil, :native? false}, ch.qos.logback/logback-core {:vsn "1.4.11", :native-prefix nil, :native? false}, org.apache.httpcomponents/httpcore-nio {:vsn "4.4.10", :native-prefix nil, :native? false}, com.fasterxml.jackson.core/jackson-core {:vsn "2.13.3", :native-prefix nil, :native? false}, slingshot {:vsn "0.12.2", :native-prefix nil, :native? false}, org.ow2.asm/asm {:vsn "9.2", :native-prefix nil, :native? false}, cheshire {:vsn "5.11.0", :native-prefix nil, :native? false}, org.swinglabs.swingx/swingx-painters {:vsn "1.6.3", :native-prefix nil, :native? false}, org.apache.httpcomponents/httpcore {:vsn "4.4.14", :native-prefix nil, :native? false}, j18n {:vsn "1.0.2", :native-prefix nil, :native? false}, org.apache.httpcomponents/httpclient-cache {:vsn "4.5.13", :native-prefix nil, :native? false}, com.miglayout/miglayout {:vsn "3.7.4", :native-prefix nil, :native? false}, potemkin {:vsn "0.4.5", :native-prefix nil, :native? false}, org.apache.poi/poi-ooxml-lite {:vsn "5.2.4", :native-prefix nil, :native? false}, org.apache.xmlbeans/xmlbeans {:vsn "5.1.1", :native-prefix nil, :native? false}, org.apache.poi/poi-ooxml {:vsn "5.2.4", :native-prefix nil, :native? false}, com.fifesoft/rsyntaxtextarea {:vsn "2.5.6", :native-prefix nil, :native? false}, commons-codec {:vsn "1.15", :native-prefix nil, :native? false}, commons-logging {:vsn "1.2", :native-prefix nil, :native? false}, org.clojure/data.csv {:vsn "1.0.1", :native-prefix nil, :native? false}, org.apache.httpcomponents/httpclient {:vsn "4.5.13", :native-prefix nil, :native? false}, org.swinglabs.swingx/swingx-core {:vsn "1.6.3", :native-prefix nil, :native? false}, com.zaxxer/SparseBitSet {:vsn "1.3", :native-prefix nil, :native? false}, com.jgoodies/forms {:vsn "1.2.1", :native-prefix nil, :native? false}, org.clojure/tools.reader {:vsn "1.3.6", :native-prefix nil, :native? false}, overtone/at-at {:vsn "1.2.0", :native-prefix nil, :native? false}, joda-time {:vsn "2.10", :native-prefix nil, :native? false}, org.swinglabs.swingx/swingx-common {:vsn "1.6.3", :native-prefix nil, :native? false}, tigris {:vsn "0.1.2", :native-prefix nil, :native? false}, clj-http {:vsn "3.12.3", :native-prefix nil, :native? false}, org.apache.commons/commons-collections4 {:vsn "4.4", :native-prefix nil, :native? false}, org.slf4j/slf4j-api {:vsn "2.0.7", :native-prefix nil, :native? false}, org.swinglabs.swingx/swingx-autocomplete {:vsn "1.6.3", :native-prefix nil, :native? false}, org.apache.logging.log4j/log4j-api {:vsn "2.20.0", :native-prefix nil, :native? false}, com.github.virtuald/curvesapi {:vsn "1.08", :native-prefix nil, :native? false}, me.raynes/fs {:vsn "1.4.6", :native-prefix nil, :native? false}, clj-tuple {:vsn "0.2.2", :native-prefix nil, :native? false}, ch.qos.logback/logback-classic {:vsn "1.4.11", :native-prefix nil, :native? false}, org.clojure/core.memoize {:vsn "1.0.253", :native-prefix nil, :native? false}, org.clojure/data.priority-map {:vsn "1.1.0", :native-prefix nil, :native? false}, commons-io {:vsn "2.8.0", :native-prefix nil, :native? false}, org.apache.httpcomponents/httpmime {:vsn "4.5.13", :native-prefix nil, :native? false}, org.clojure/core.cache {:vsn "1.0.225", :native-prefix nil, :native? false}, org.clojure/core.async {:vsn "1.6.673", :native-prefix nil, :native? false}, com.fasterxml.jackson.dataformat/jackson-dataformat-smile {:vsn "2.13.3", :native-prefix nil, :native? false}, seesaw {:vsn "1.5.0", :native-prefix nil, :native? false}}}]