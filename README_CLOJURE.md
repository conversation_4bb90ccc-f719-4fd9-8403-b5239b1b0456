# Instagram Reels Parser v1.0 - Clojure Edition

Полнофункциональное приложение для парсинга статистики Instagram Reels с использованием HikerAPI, переписанное на Clojure.

## 🚀 Быстрый старт

### Требования
- ✅ Java 11+ (установлена: Java 21)
- ⚠️ Leiningen 2.9.0+ (требует установки)

### Установка и запуск
1. **Установите Leiningen** (если не установлен):
   - См. подробные инструкции в `INSTALL_LEININGEN.md`
   - Или используйте `build_manual.bat` для ручной сборки

2. **Соберите проект**:
   ```cmd
   build.bat
   ```

3. **Настройте config.ini** с вашим API ключом

4. **Запустите приложение**:
   ```cmd
   run.bat                    # GUI режим (рекомендуется)
   run_cli.bat data.xlsx      # CLI режим
   schedule.bat data.xlsx     # Планировщик
   ```

## 📁 Файлы для развертывания

- `build.bat` - автоматическая сборка проекта
- `build_manual.bat` - ручная сборка без Leiningen
- `run.bat` - запуск в GUI режиме
- `run_cli.bat` - запуск в CLI режиме
- `schedule.bat` - планировщик задач
- `DEPLOYMENT.md` - подробная инструкция по развертыванию
- `INSTALL_LEININGEN.md` - инструкция по установке Leiningen

## Особенности

- ✅ Полностью функциональная версия на Clojure
- ✅ Графический интерфейс с использованием Seesaw
- ✅ Асинхронная обработка данных
- ✅ Планировщик задач для автоматического запуска
- ✅ Работа с Excel файлами через Apache POI
- ✅ HTTP клиент для работы с HikerAPI
- ✅ Конфигурация через INI файлы
- ✅ Логирование с ротацией файлов

## Требования

- Java 11 или выше ✅
- Leiningen 2.9.0 или выше ⚠️
- HikerAPI ключ

## Установка

1. Убедитесь, что Java установлена:
```cmd
java -version
```

2. Установите Leiningen (см. INSTALL_LEININGEN.md)

3. Соберите проект:
```bash
lein deps
```

3. Настройте конфигурацию в `config.ini`:
```ini
[API]
access_key = YOUR_HIKER_API_KEY_HERE
base_url = https://api.hikerapi.com/v2
debug = false

[SETTINGS]
daily_run_time = 09:00
request_delay = 2
max_retries = 3
request_timeout = 30

[FILES]
url_column = Ссылка на Reels
account_column = Account_Name
save_mode = same_file
```

## Запуск

### Запуск с GUI (рекомендуется)
```bash
lein run
```

### Запуск из командной строки
```bash
lein run -- --file data.xlsx
```

### Сборка standalone JAR
```bash
lein uberjar
java -jar target/uberjar/instagram-reels-parser-1.0.0-standalone.jar
```

## Структура проекта

```
src/instagram_reels_parser/
├── core.clj          # Главная точка входа
├── config.clj        # Управление конфигурацией
├── api.clj           # HTTP клиент и API взаимодействие
├── utils.clj         # Утилиты для парсинга URL и обработки данных
├── excel.clj         # Работа с Excel файлами
├── parser.clj        # Основная логика парсинга
├── gui.clj           # Графический интерфейс
└── scheduler.clj     # Планировщик задач

resources/
└── logback.xml       # Конфигурация логирования

test/instagram_reels_parser/
└── ...               # Тесты
```

## Использование

### Графический интерфейс

1. Запустите приложение: `lein run`
2. Выберите Excel файл с колонкой "Ссылка на Reels"
3. Настройте API ключ в Settings
4. Нажмите "Start Parsing"

### Программный интерфейс

```clojure
(require '[instagram-reels-parser.config :as config]
         '[instagram-reels-parser.parser :as parser])

;; Загрузить конфигурацию
(config/load-config! "config.ini")

;; Запустить парсинг
(parser/parse-and-save "data.xlsx"
                       :progress-callback #(println "Progress:" %)
                       :log-callback #(println "Log:" %))
```

### Планировщик задач

```clojure
(require '[instagram-reels-parser.scheduler :as scheduler])

;; Запланировать ежедневный парсинг в 09:00
(scheduler/schedule-daily-parsing "data.xlsx" "09:00")

;; Запланировать одноразовый парсинг через 60 секунд
(scheduler/schedule-one-time-parsing "data.xlsx" 60)

;; Отменить все задачи
(scheduler/cancel-all-jobs)
```

## API

### Конфигурация

```clojure
;; Загрузить конфигурацию
(config/load-config! "config.ini")

;; Получить значение
(config/get-config [:api :access-key])

;; Установить значение
(config/set-config! [:api :access-key] "new-key")

;; Сохранить конфигурацию
(config/save-config! "config.ini")
```

### Парсинг

```clojure
;; Валидация файла
(parser/validate-excel-file "data.xlsx")

;; Синхронный парсинг
(parser/parse-and-save "data.xlsx")

;; Асинхронный парсинг
(let [result-chan (parser/parse-async "data.xlsx")]
  (async/<!! result-chan))
```

### Работа с Excel

```clojure
;; Чтение Excel файла
(excel/read-excel-file "data.xlsx")

;; Запись Excel файла
(excel/write-excel-file "output.xlsx" data headers)

;; Обновление Excel файла
(excel/update-excel-file "data.xlsx" updates)
```

## Отличия от Python версии

### Преимущества Clojure версии:

1. **Функциональное программирование**: Неизменяемые структуры данных, отсутствие побочных эффектов
2. **Конкурентность**: Встроенная поддержка асинхронности через core.async
3. **JVM экосистема**: Доступ к богатой экосистеме Java библиотек
4. **Производительность**: Компиляция в байт-код JVM
5. **Надежность**: Строгая типизация и функциональные принципы

### Архитектурные улучшения:

1. **Модульность**: Четкое разделение ответственности между модулями
2. **Тестируемость**: Функциональный подход упрощает тестирование
3. **Расширяемость**: Легко добавлять новые функции без изменения существующего кода
4. **Обработка ошибок**: Использование исключений и монад для обработки ошибок

## Разработка

### Запуск тестов
```bash
lein test
```

### REPL разработка
```bash
lein repl
```

### Форматирование кода
```bash
lein cljfmt fix
```

### Проверка зависимостей
```bash
lein ancient
```

## Лицензия

MIT License

## Поддержка

Для вопросов и предложений создавайте issues в репозитории.
