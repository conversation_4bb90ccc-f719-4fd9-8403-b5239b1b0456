(ns instagram-reels-parser.api
  "HTTP client and API interaction for HikerAPI"
  (:require [clj-http.client :as http]
            [cheshire.core :as json]
            [clojure.tools.logging :as log]
            [instagram-reels-parser.config :as config]
            [clj-time.core :as time]
            [clj-time.format :as time-format]))

(def default-headers
  {"accept" "application/json"
   "User-Agent" "Instagram-Reels-Parser-Clojure/1.0"})

(defn get-nested-value
  "Get value from nested map by dot-separated path"
  [data path]
  (try
    (let [keys (clojure.string/split path #"\.")
          value (reduce get data (map keyword keys))]
      (when (and value (or (number? value) (string? value)))
        (if (string? value)
          (try (Integer/parseInt value) (catch Exception _ nil))
          (int value))))
    (catch Exception _
      nil)))

(defn parse-api-response
  "Parse API response into unified format"
  [data]
  (let [timestamp (time-format/unparse
                   (time-format/formatter "yyyy-MM-dd HH:mm:ss")
                   (time/now))
        
        ;; Try different response structures
        possible-structures [{:views ["view_count" "play_count" "video_view_count"]
                             :likes ["like_count" "likes" "edge_media_preview_like.count"]
                             :comments ["comment_count" "comments" "edge_media_to_comment.count"]}
                            {:views ["statistics.view_count" "metrics.views"]
                             :likes ["statistics.like_count" "metrics.likes"]
                             :comments ["statistics.comment_count" "metrics.comments"]}]
        
        result (atom {:views 0 :likes 0 :comments 0 :timestamp timestamp})]
    
    (doseq [structure possible-structures]
      (doseq [[metric paths] structure]
        (when (zero? (get @result metric))
          (doseq [path paths]
            (when-let [value (get-nested-value data path)]
              (swap! result assoc metric value)
              (break))))))
    
    @result))

(defn create-error-response
  "Create standard error response"
  [error-message]
  {:error error-message
   :views 0
   :likes 0
   :comments 0
   :timestamp (time-format/unparse
               (time-format/formatter "yyyy-MM-dd HH:mm:ss")
               (time/now))})

(defn fetch-reel-data
  "Fetch reel data from HikerAPI v2"
  [shortcode]
  (try
    (let [access-key (config/get-config [:api :access-key])
          base-url (config/get-config [:api :base-url])
          timeout (config/get-config [:settings :request-timeout] 30)
          max-retries (config/get-config [:settings :max-retries] 3)
          debug-mode (config/get-config [:api :debug] false)]
      
      ;; Validate API key
      (when (or (nil? access-key) (= access-key "YOUR_ACCESS_KEY_HERE"))
        (let [error-msg "API access key not configured in config.ini"]
          (log/error error-msg)
          (throw (ex-info error-msg {:type :config-error}))))
      
      ;; API endpoints to try
      (let [endpoints [["https://api.hikerapi.com/v1/media/by/code" :get {:code shortcode :access_key access-key}]
                       ["https://api.hikerapi.com/v2/media/info/by/code" :get {:code shortcode :access_key access-key}]
                       ["https://api.hikerapi.com/v1/media/by/url" :get {:url (str "https://www.instagram.com/reel/" shortcode "/") :access_key access-key}]
                       ["https://api.hikerapi.com/v2/media/info/by/url" :get {:url (str "https://www.instagram.com/reel/" shortcode "/") :access_key access-key}]]]
        
        ;; Try each endpoint with retries
        (loop [endpoints-to-try endpoints
               attempt 0]
          (if (or (empty? endpoints-to-try) (>= attempt max-retries))
            (create-error-response (str "Failed to get data after " max-retries " attempts"))
            
            (let [[endpoint method params] (first endpoints-to-try)]
              (try
                (when debug-mode
                  (log/info "Trying endpoint:" endpoint "with shortcode:" shortcode))
                
                (let [response (case method
                                 :get (http/get endpoint
                                                {:headers default-headers
                                                 :query-params params
                                                 :socket-timeout (* timeout 1000)
                                                 :connection-timeout (* timeout 1000)
                                                 :throw-exceptions false})
                                 :post (http/post endpoint
                                                  {:headers default-headers
                                                   :form-params params
                                                   :socket-timeout (* timeout 1000)
                                                   :connection-timeout (* timeout 1000)
                                                   :throw-exceptions false}))]
                  
                  (when debug-mode
                    (log/debug "Response status:" (:status response))
                    (log/debug "Response headers:" (:headers response))
                    (log/debug "Response body:" (subs (:body response) 0 (min 500 (count (:body response))))))
                  
                  (case (:status response)
                    200 (try
                          (let [data (json/parse-string (:body response) true)]
                            (parse-api-response data))
                          (catch Exception e
                            (let [error-msg (str "JSON decode error: " (.getMessage e))]
                              (log/error error-msg "Response:" (subs (:body response) 0 (min 200 (count (:body response)))))
                              (create-error-response error-msg))))
                    
                    401 (create-error-response "Authorization error: invalid API access key")
                    404 (create-error-response (str "Reel " shortcode " not found (404)"))
                    429 (do
                          (log/warn "Rate limit exceeded. Waiting 60 seconds...")
                          (Thread/sleep 60000)
                          (recur endpoints-to-try (inc attempt)))
                    
                    ;; Other errors - try next endpoint
                    (do
                      (log/error "API error" (:status response) ":" (subs (:body response) 0 (min 200 (count (:body response)))))
                      (recur (rest endpoints-to-try) attempt))))
                
                (catch Exception e
                  (log/error e "Request error for endpoint:" endpoint)
                  ;; Exponential backoff
                  (when (< attempt (dec max-retries))
                    (let [wait-time (* (Math/pow 2 attempt) 2000)]
                      (log/info "Retrying in" (/ wait-time 1000) "seconds...")
                      (Thread/sleep wait-time)))
                  (recur (rest endpoints-to-try) (inc attempt)))))))))
    
    (catch clojure.lang.ExceptionInfo e
      (if (= (:type (ex-data e)) :config-error)
        (create-error-response (.getMessage e))
        (do
          (log/error e "Unexpected error")
          (create-error-response (str "Unexpected error: " (.getMessage e))))))
    
    (catch Exception e
      (log/error e "Unexpected error")
      (create-error-response (str "Unexpected error: " (.getMessage e))))))
