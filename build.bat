@echo off
REM Instagram Reels Parser - Build Script
REM This script builds the standalone JAR file

echo Building Instagram Reels Parser...

REM Check if Leining<PERSON> is installed
lein version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: <PERSON><PERSON><PERSON> is not installed or not in PATH
    echo.
    echo Please install Leiningen:
    echo 1. Download lein.bat from https://leiningen.org/
    echo 2. Place it in a directory that's in your PATH
    echo 3. Run 'lein self-install' to complete installation
    echo.
    echo Alternative: Use the manual JAR compilation method
    echo See DEPLOYMENT.md for detailed instructions
    pause
    exit /b 1
)

REM Clean previous builds
echo Cleaning previous builds...
lein clean

REM Download dependencies
echo Downloading dependencies...
lein deps

REM Run tests
echo Running tests...
lein test
if %errorlevel% neq 0 (
    echo Warning: Some tests failed, but continuing with build...
)

REM Build standalone JAR
echo Building standalone JAR...
lein uberjar

if %errorlevel% eq 0 (
    echo.
    echo ✅ Build completed successfully!
    echo JAR file: target\uberjar\instagram-reels-parser-1.0.0-standalone.jar
    echo.
    echo You can now run the application with:
    echo   run.bat                    # GUI mode
    echo   run_cli.bat data.xlsx      # CLI mode
    echo   schedule.bat data.xlsx     # Scheduler mode
    echo.
) else (
    echo ❌ Build failed!
    pause
    exit /b 1
)

pause
