@echo off
REM Instagram Reels Parser - Scheduler Mode Launcher
REM Usage: schedule.bat path\to\excel\file.xlsx [HH:mm]

if "%1"=="" (
    echo Usage: schedule.bat path\to\excel\file.xlsx [HH:mm]
    echo Example: schedule.bat data.xlsx 09:00
    echo Default time: 09:00
    pause
    exit /b 1
)

set EXCEL_FILE=%1
set SCHEDULE_TIME=%2
if "%SCHEDULE_TIME%"=="" set SCHEDULE_TIME=09:00

echo Starting Instagram Reels Parser in scheduler mode...
echo File: %EXCEL_FILE%
echo Scheduled time: %SCHEDULE_TIME%

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java 11 or higher
    pause
    exit /b 1
)

REM Check if JAR file exists
if not exist "target\uberjar\instagram-reels-parser-1.0.0-standalone.jar" (
    echo JAR file not found. Building project...
    call lein uberjar
    if %errorlevel% neq 0 (
        echo Error: Failed to build project
        pause
        exit /b 1
    )
)

REM Launch the application in scheduler mode
java -jar target\uberjar\instagram-reels-parser-1.0.0-standalone.jar --file "%EXCEL_FILE%" --schedule --time %SCHEDULE_TIME%

if %errorlevel% neq 0 (
    echo Application exited with error code %errorlevel%
    pause
)
