(ns instagram-reels-parser.core
  "Main entry point for Instagram Reels Parser"
  (:require [clojure.tools.cli :as cli]
            [clojure.tools.logging :as log]
            [instagram-reels-parser.config :as config]
            [instagram-reels-parser.gui :as gui]
            [instagram-reels-parser.parser :as parser]
            [instagram-reels-parser.scheduler :as scheduler])
  (:gen-class))

(def cli-options
  [["-f" "--file FILE" "Excel file path"
    :validate [#(.exists (java.io.File. %)) "File must exist"]]
   ["-g" "--gui" "Start with GUI (default)"]
   ["-c" "--cli" "Run in command line mode"]
   ["-s" "--schedule" "Schedule daily parsing"]
   ["-t" "--time TIME" "Daily run time (HH:mm format, default: 09:00)"
    :default "09:00"]
   ["-h" "--help" "Show help"]])

(defn usage [options-summary]
  (->> ["Instagram Reels Parser v1.0 - Clojure Edition"
        ""
        "Usage: program-name [options]"
        ""
        "Options:"
        options-summary
        ""
        "Examples:"
        "  lein run                              # Start with GUI"
        "  lein run -- --file data.xlsx --cli   # Parse file in CLI mode"
        "  lein run -- --schedule --time 14:30  # Schedule daily parsing at 14:30"
        ""]
       (clojure.string/join \newline)))

(defn error-msg [errors]
  (str "The following errors occurred while parsing your command:\n\n"
       (clojure.string/join \newline errors)))

(defn validate-args
  "Validate command line arguments."
  [args]
  (let [{:keys [options arguments errors summary]} (cli/parse-opts args cli-options)]
    (cond
      (:help options) ; help => exit OK with usage summary
      {:exit-message (usage summary) :ok? true}

      errors ; errors => exit with description of errors
      {:exit-message (error-msg errors)}

      ;; Default to GUI mode if no specific mode specified
      (and (not (:cli options)) (not (:schedule options)))
      {:action :gui :options options}

      (:cli options)
      (if (:file options)
        {:action :cli :options options}
        {:exit-message "CLI mode requires --file option"})

      (:schedule options)
      {:action :schedule :options options}

      :else ; failed custom validation => exit with usage summary
      {:exit-message (usage summary)})))

(defn run-cli-mode
  "Run parser in CLI mode"
  [file-path]
  (println "🚀 Starting Instagram Reels Parser in CLI mode")
  (println "📁 File:" file-path)

  (let [result (parser/parse-and-save file-path
                                      :progress-callback #(println (str "Progress: " (int %) "%"))
                                      :log-callback println)]
    (if (:success result)
      (do
        (println "✅ Parsing completed successfully!")
        (println "📊 Statistics:" (:stats result))
        (System/exit 0))
      (do
        (println "❌ Parsing failed:" (:error result))
        (System/exit 1)))))

(defn run-schedule-mode
  "Run parser in schedule mode"
  [options]
  (let [file-path (:file options)
        time-str (:time options)]

    (if file-path
      (do
        (println "⏰ Scheduling daily parsing")
        (println "📁 File:" file-path)
        (println "🕒 Time:" time-str)

        ;; Add shutdown hook for clean scheduler shutdown
        (scheduler/add-shutdown-hook)

        (if (scheduler/schedule-daily-parsing file-path time-str
                                             :log-callback println)
          (do
            (println "✅ Daily parsing scheduled successfully!")
            (println "Press Ctrl+C to stop the scheduler")
            ;; Keep the program running
            (while true
              (Thread/sleep 60000)  ; Sleep for 1 minute
              (when (scheduler/is-parsing-scheduled?)
                (let [next-time (scheduler/get-next-scheduled-time)]
                  (println (str "⏰ Next execution: " next-time))))))
          (do
            (println "❌ Failed to schedule daily parsing")
            (System/exit 1))))
      (do
        (println "❌ Schedule mode requires --file option")
        (System/exit 1)))))

(defn -main
  "Main entry point"
  [& args]
  (let [{:keys [action options exit-message ok?]} (validate-args args)]
    (if exit-message
      (do
        (println exit-message)
        (System/exit (if ok? 0 1)))
      (do
        ;; Initialize configuration
        (config/load-config! "config.ini")
        (log/info "Instagram Reels Parser started")

        (case action
          :gui (do
                 (log/info "Starting GUI mode")
                 (gui/start-gui options))

          :cli (do
                 (log/info "Starting CLI mode with file:" (:file options))
                 (run-cli-mode (:file options)))

          :schedule (do
                      (log/info "Starting scheduler mode")
                      (run-schedule-mode options)))))))
