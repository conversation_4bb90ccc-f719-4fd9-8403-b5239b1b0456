@echo off
REM Instagram Reels Parser - CLI Mode Launcher
REM Usage: run_cli.bat path\to\excel\file.xlsx

if "%1"=="" (
    echo Usage: run_cli.bat path\to\excel\file.xlsx
    echo Example: run_cli.bat data.xlsx
    pause
    exit /b 1
)

echo Starting Instagram Reels Parser in CLI mode...
echo File: %1

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java 11 or higher
    pause
    exit /b 1
)

REM Check if JAR file exists
if not exist "target\uberjar\instagram-reels-parser-1.0.0-standalone.jar" (
    echo JAR file not found. Building project...
    call lein uberjar
    if %errorlevel% neq 0 (
        echo Error: Failed to build project
        pause
        exit /b 1
    )
)

REM Launch the application in CLI mode
java -jar target\uberjar\instagram-reels-parser-1.0.0-standalone.jar --file "%1" --cli

if %errorlevel% neq 0 (
    echo <PERSON> exited with error code %errorlevel%
    pause
)
