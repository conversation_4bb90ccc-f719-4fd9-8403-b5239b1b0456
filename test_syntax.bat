@echo off
REM Simple syntax check for Clojure files

echo Checking Clojure syntax...

REM Check if we have any obvious syntax errors by looking for common issues
echo Checking for common syntax issues...

findstr /R /C:"^[[:space:]]*)" src\instagram_reels_parser\*.clj >nul
if %errorlevel% equ 0 (
    echo Warning: Found lines starting with closing parenthesis
)

findstr /R /C:"([[:space:]]*$" src\instagram_reels_parser\*.clj >nul
if %errorlevel% equ 0 (
    echo Warning: Found lines ending with opening parenthesis only
)

echo.
echo Basic syntax check completed.
echo For full testing, install Leiningen and run: lein test
echo.

REM List all created files
echo Created files:
echo.
echo Core modules:
dir /B src\instagram_reels_parser\*.clj
echo.
echo Tests:
dir /B test\instagram_reels_parser\*.clj 2>nul
echo.
echo Scripts:
dir /B *.bat
echo.
echo Documentation:
dir /B *.md
echo.
echo Configuration:
dir /B *.clj *.xml 2>nul

pause
