# Instagram Reels Parser

Приложение с графическим интерфейсом для парсинга данных Instagram Reels через HikerAPI SaaS. Предназначено для ежедневного мониторинга просмотров до 150 аккаунтов с автоматической записью динамики по дням в один Excel файл.

## 🚀 Особенности

- **Графический интерфейс** - Удобный GUI на Tkinter с кнопками управления
- **Один файл** - Все данные и динамика в одном Excel файле
- **Автоматическая группировка по дням** - Данные записываются в колонки по номерам дней (1, 2, 3... 31)
- **Массовый парсинг** - Обработка до 150 аккаунтов в день
- **Управление автозапуском** - Включение/выключение автоматизации одной кнопкой
- **Полная автоматизация** - Автозапуск в Windows 11 без участия пользователя
- **Headless режим** - Версия без GUI для серверного использования
- **Планировщик заданий** - Интеграция с Windows Task Scheduler
- **Проверка статуса** - Мониторинг состояния автозапуска в реальном времени
- **Безопасность** - Автоматические резервные копии перед изменениями
- **Надежность** - Обработка ошибок, повторные попытки, валидация данных
- **Конфигурируемость** - Внешний файл настроек
- **Логирование** - Подробные логи выполнения

## 📋 Требования

- **ОС**: Windows 11 (или Windows 10)
- **Python**: 3.9 или выше
- **API**: Ключ HikerAPI SaaS
- **Excel**: Файл с колонкой `Ссылка на Reels` и колонками дней (1-31)

## 🛠 Установка

1. **Клонирование репозитория**
```bash
git clone <repository_url>
cd instagram-reels-parser
```

2. **Установка зависимостей**
```bash
pip install -r requirements.txt
```

3. **Настройка API ключа**
   - Откройте `config.ini`
   - Замените `YOUR_HIKER_API_KEY_HERE` на ваш ключ HikerAPI
   - При необходимости настройте другие параметры

## 🚀 Быстрый старт

1. **Установите зависимости**: `pip install -r requirements.txt`
2. **Запустите приложение**: `python instagram_reels_parser.py`
3. **Настройте API ключ**: Кнопка "Настройки" → введите HikerAPI ключ
4. **Выберите Excel файл** с колонкой "Ссылка на Reels"
5. **Включите автозапуск**: Кнопка "Включить автозапуск" → готово!

> 🎉 **Всё!** Парсер будет работать автоматически каждый день в 09:00

---

## 📖 Подробное использование

### 1. Подготовка данных

Создайте Excel файл с обязательной структурой:
- **Колонка `Ссылка на Reels`** - ссылки на Instagram Reels
- **Колонки с номерами дней** - `1`, `2`, `3`... `31` (создаются автоматически)

**Пример структуры:**
| Ссылка на Reels | 1 | 2 | 3 | ... | 27 | 28 | ... | 31 |
|-----------------|---|---|---|-----|----|----|-----|----| 
| https://www.instagram.com/reel/DKjo16YqYSo/ | 0 | 0 | 1250 | ... | 5967 | 0 | ... | 0 |
| https://www.instagram.com/reel/DKUE5j_o89M/ | 0 | 0 | 890  | ... | 1383 | 0 | ... | 0 |

> 💡 **Важно**: Данные автоматически записываются в колонку текущего дня месяца

### 2. Запуск приложения

#### Режим с GUI (интерактивный):
```bash
python instagram_reels_parser.py
```

#### Headless режим (автоматический):
```bash
python headless_parser.py -v
```

### 3. Работа с интерфейсом

#### Основные кнопки:
1. **"Выбрать файл"** - Выбор Excel файла с ссылками на Reels
2. **"Запустить парсинг"** - Разовый парсинг (данные запишутся в колонку текущего дня)
3. **"Остановить"** - Прерывание текущего парсинга
4. **"Запланировать ежедневно"** - Внутренний планировщик (работает пока GUI открыт)

#### Управление автозапуском:
5. **"Включить автозапуск"** - Создаёт задачу Windows Task Scheduler на 09:00 ежедневно
6. **"Выключить автозапуск"** - Удаляет задачу автозапуска из системы
7. **"Проверить статус"** - Показывает текущий статус автоматического запуска

#### Дополнительно:
8. **"Настройки"** - Конфигурация API ключа, времени запуска и других параметров

> 💡 **Рекомендация**: Используйте кнопки автозапуска для полной автоматизации без участия пользователя

### 4. Создание тестового файла

Используйте готовый скрипт для создания тестового файла:
```bash
python create_test_reels.py
```
Будет создан файл `test_reels_format.xlsx` с правильной структурой.

## 🤖 Автоматизация парсинга

### 🎯 Управление автозапуском через GUI (рекомендуется)

Самый простой способ настроить ежедневный автозапуск:

1. **Запустите приложение**:
   ```bash
   python instagram_reels_parser.py
   ```

2. **Выберите Excel файл** с вашими Reels ссылками

3. **Управляйте автозапуском кнопками**:
   - 🟢 **"Включить автозапуск"** - создаёт задачу Windows на 09:00 ежедневно
   - 🔍 **"Проверить статус"** - показывает текущий статус автозапуска
   - 🔴 **"Выключить автозапуск"** - отключает автоматический запуск

4. **Готово!** Парсер будет работать автоматически каждый день

### 📊 Статусы автозапуска:

- ✅ **"Автозапуск ВКЛЮЧЕН"** - задача активна, будет выполняться ежедневно
- ❌ **"Автозапуск ОТКЛЮЧЕН"** - автоматический запуск не настроен
- ⚠️ **"Ошибка настройки"** - требуются права администратора

### 🔧 Дополнительные режимы:

#### Headless режим (без GUI):
```bash
# Обычный запуск
python headless_parser.py

# С подробными логами
python headless_parser.py -v

# Указать конкретный файл
python headless_parser.py -f data.xlsx

# Тестовый режим (без реального парсинга)
python headless_parser.py --dry-run
```

#### Планирование в GUI:
- **"Запланировать ежедневно"** - внутренний планировщик (работает пока GUI открыт)
- Время запуска настраивается в **"Настройки"** → **"Время ежедневного запуска"**

### ⚡ Преимущества GUI управления:

✅ **Простота** - всё в одном интерфейсе  
✅ **Наглядность** - видите статус сразу  
✅ **Безопасность** - легко включить/выключить  
✅ **Надёжность** - использует Windows Task Scheduler  
✅ **Автономность** - работает даже при закрытой программе

## 💡 Примеры использования

### Ежедневный мониторинг
```
День 1: Запуск парсинга → данные в колонке "1"
День 2: Запуск парсинга → данные в колонке "2"  
День 3: Запуск парсинга → данные в колонке "3"
...
```

### Анализ динамики
Сравнивайте просмотры между днями:
- **Рост**: 1250 → 2100 → 3500 (растущий тренд)
- **Падение**: 5000 → 4200 → 3800 (снижение активности)
- **Стабильность**: 1500 → 1520 → 1510 (стабильные показатели)

### Автоматизация
1. **GUI режим**: Запланируйте ежедневный парсинг в 09:00 через интерфейс
2. **Системная автоматизация**: Настройте автозапуск в Windows Task Scheduler
3. **Мониторинг**: Проверяйте логи каждые несколько дней
4. **Архивирование**: В конце месяца сохраните файл с полными данными

## ⚙️ Конфигурация

Основные настройки в `config.ini`:

```ini
[API]
hiker_api_key = YOUR_API_KEY              # Ваш ключ HikerAPI
base_url = https://hikerapi.com/api       # Базовый URL API

[SETTINGS] 
daily_run_time = 09:00                    # Время ежедневного запуска
request_delay = 2                         # Задержка между запросами (сек)
max_retries = 3                           # Максимум попыток при ошибке
request_timeout = 30                      # Таймаут запроса (сек)

[FILES]
url_column = Ссылка на Reels              # Название колонки с URL
save_mode = same_file                     # Режим сохранения в тот же файл
```

## 📊 Результаты

Данные обновляются прямо в исходном Excel файле:
- **Колонка текущего дня** - количество просмотров записывается в колонку с номером дня
- **Автоматические бэкапы** - создается резервная копия `*_backup_YYYYMMDD_HHMMSS.xlsx`
- **Динамика по дням** - можно отслеживать изменения просмотров день за днем
- **Нулевые значения** - при ошибке парсинга записывается 0

**Пример результата:**
```
📅 27 января - данные записались в колонку "27"
📅 28 января - данные запишутся в колонку "28"
📅 29 января - данные запишутся в колонку "29"
```

**Логи выполнения:**
- Подробная информация в GUI (интерактивный режим)
- Файл `instagram_parser.log` для детального анализа
- Файл `headless_parser_YYYYMMDD.log` для автоматического режима
- Статистика успешных и неудачных запросов

## 🔧 Устранение неполадок

### Проблемы с API

1. **"API ключ не настроен"**
   - Проверьте `config.ini`
   - Убедитесь, что ключ заменен на настоящий

2. **"Неверный API ключ"**
   - Проверьте правильность ключа
   - Убедитесь в активности подписки HikerAPI

3. **"Rate limit exceeded"**
   - Приложение автоматически ждет и повторяет
   - Увеличьте `request_delay` в настройках

### Проблемы с файлами

1. **"Столбец 'Ссылка на Reels' не найден"**
   - Проверьте название колонки в Excel
   - Измените `url_column` в `config.ini` при необходимости

2. **"Не найдено валидных Instagram Reels URL"**
   - Проверьте формат ссылок  
   - Поддерживаются: `/reel/`, `/reels/`, `/p/`
   - Убедитесь, что ссылки не пустые

3. **"Колонка '27' создана автоматически"**
   - Это нормально - программа создает колонки дней автоматически
   - Каждый день создается новая колонка с номером дня

### Проблемы с автоматизацией

1. **"Задача не запускается в Планировщике"**
   - Проверьте права пользователя (запуск от администратора)
   - Убедитесь, что Python в PATH
   - Проверьте пути к файлам в задаче

2. **"Headless parser не запускается"**
   - Убедитесь, что все файлы в той же папке
   - Проверьте логи в `headless_parser_YYYYMMDD.log`
   - Запустите `python headless_parser.py --dry-run` для теста

3. **"PowerShell скрипт заблокирован"**
   - Выполните: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
   - Запустите PowerShell от имени администратора

## 🚨 Ограничения и риски

### HikerAPI
- **Лимиты запросов**: зависят от тарифного плана
- **Блокировки Instagram**: при превышении частоты запросов
- **Изменения API**: структура ответа может измениться

### Instagram
- **Приватные аккаунты**: недоступны для парсинга
- **ToS нарушения**: массовый парсинг может нарушать условия использования
- **Блокировки**: при подозрительной активности

### Технические
- **Сбои сети**: влияют на стабильность парсинга
- **Большие файлы**: Excel файлы >1MB могут тормозить
- **Многопоточность**: текущая версия не поддерживает
- **Смена месяца**: при переходе на новый месяц колонки начинаются с "1"
- **Бэкапы**: занимают дополнительное место на диске

## 📈 Рекомендации по оптимизации

### Для больших объемов (>150 аккаунтов)
1. **Увеличьте задержки** - `request_delay = 3-5`
2. **Используйте прокси** - настройте в HikerAPI
3. **Разделите данные** - по 100-150 Reels на файл

### Для стабильности  
1. **Автоматические бэкапы** - создаются перед каждым парсингом
2. **Мониторинг логов** - проверяйте `instagram_parser.log`
3. **Обновление зависимостей** - регулярно обновляйте библиотеки
4. **Очистка бэкапов** - периодически удаляйте старые backup файлы
5. **Архивирование** - в конце месяца сохраняйте файл с полными данными

## 📁 Структура проекта

```
📁 Проект/
├── 📄 instagram_reels_parser.py      # Основное GUI приложение
├── 📄 headless_parser.py             # Headless версия для автоматизации
├── 📄 auto_parser.bat                # Bat-файл для Windows автозапуска
├── 📄 setup_task_scheduler.ps1       # PowerShell скрипт настройки автозапуска
├── 📄 create_test_reels.py           # Генератор тестовых файлов
├── 📄 config.ini                     # Конфигурационный файл
├── 📄 requirements.txt               # Зависимости Python
├── 📄 README.md                      # Основная документация
├── 📄 АВТОЗАПУСК_ИНСТРУКЦИЯ.md       # Подробная инструкция по автоматизации
├── 📊 test_reels_format.xlsx         # Тестовый файл с правильной структурой
├── 📊 example_reels.xlsx             # Пример файла с данными
├── 📊 example_reels_stats.xlsx       # Пример файла со статистикой
└── 📋 Логи/
    ├── instagram_parser.log          # Основные логи
    └── headless_parser_YYYYMMDD.log  # Логи автоматического режима
```

## 🔄 Будущие улучшения

### В разработке:
- [x] Полная автоматизация через Windows Task Scheduler
- [x] Headless режим для серверного использования
- [x] Автоматическое создание бэкапов
- [x] Детальное логирование всех операций

### Планируется:
- [ ] Поддержка TikTok и YouTube Shorts
- [ ] Многопоточный парсинг
- [ ] Интеграция с Google Sheets
- [ ] Уведомления в Telegram при достижении целевых показателей
- [ ] Анализ трендов и графики динамики
- [ ] Автоматическое архивирование данных по месяцам
- [ ] Экспорт в различные форматы (CSV, JSON)
- [ ] REST API для интеграции
- [ ] Дашборд для визуализации данных
- [ ] Поддержка Linux и macOS для автоматизации

## 📞 Поддержка

При возникновении проблем:
1. **Основные проблемы**: Проверьте логи в `instagram_parser.log`
2. **Проблемы автоматизации**: Проверьте логи в `headless_parser_YYYYMMDD.log`
3. **Конфигурация**: Убедитесь в правильности настроек в `config.ini`
4. **API**: Проверьте доступность HikerAPI и корректность ключа
5. **Планировщик**: Используйте встроенные инструменты Windows для диагностики
6. **Подробная помощь**: См. `АВТОЗАПУСК_ИНСТРУКЦИЯ.md` для решения проблем автоматизации

### Быстрая диагностика:
```bash
# Тест headless режима
python headless_parser.py --dry-run

# Тест с подробным выводом
python headless_parser.py -v

# Проверка конфигурации
python -c "import configparser; c=configparser.ConfigParser(); c.read('config.ini'); print('API Key:', c.get('API', 'hiker_api_key')[:10]+'...')"
```

### Проверка автоматизации:
```powershell
# Статус задачи
Get-ScheduledTask -TaskName "Instagram_Reels_Parser_Daily" | Get-ScheduledTaskInfo

# Тестовый запуск
Start-ScheduledTask -TaskName "Instagram_Reels_Parser_Daily"
```

## 📄 Лицензия

Данный проект предназначен для образовательных и исследовательских целей. Убедитесь в соответствии использования условиям Instagram и HikerAPI.

## ⚠️ Дисклеймер

Автор не несет ответственности за:
- Нарушение условий использования Instagram
- Блокировки аккаунтов
- Потерю данных
- Финансовые потери от использования HikerAPI

Используйте на свой страх и риск с соблюдением всех применимых законов и условий сервисов. 