# Changelog

## [2.1.0] - 2024-01-27

### 🎯 Управление автозапуском через GUI

#### Новые кнопки в интерфейсе:
- **"Включить автозапуск"** - Создание задачи Windows Task Scheduler одной кнопкой
- **"Выключить автозапуск"** - Удаление задачи автозапуска из системы
- **"Проверить статус"** - Мониторинг состояния автоматического запуска

#### Улучшения интерфейса:
- **Визуальный разделитель** между основными кнопками и управлением автозапуском
- **Статус-бар** показывает текущее состояние автозапуска
- **Подробные логи** всех операций с автозапуском
- **Диалоги уведомлений** с информацией о результатах операций

#### Техническая реализация:
- **Интеграция с PowerShell** для управления Windows Task Scheduler
- **Автоматическая проверка** существования необходимых файлов
- **Обработка ошибок** с понятными сообщениями пользователю
- **Безопасное выполнение** команд через subprocess

### 🔧 Преимущества новой версии:

✅ **Простота использования** - управление автозапуском без командной строки  
✅ **Наглядность** - видимый статус автозапуска в интерфейсе  
✅ **Безопасность** - легкое включение/выключение автоматизации  
✅ **Интеграция** - всё управление в одном приложении  
✅ **Надёжность** - использует проверенный Windows Task Scheduler  

### 📖 Обновленная документация:
- **README.md** - Упрощен раздел автоматизации, акцент на GUI управление
- **Удалены сложные инструкции** - оставлен только простой способ через кнопки
- **Новые примеры использования** - демонстрация работы с кнопками автозапуска

---

## [2.0.0] - 2024-01-27

### 🚀 Новые возможности

#### Полная автоматизация Windows 11
- **Headless режим** - Версия без GUI для автоматического запуска
- **Windows Task Scheduler** - Интеграция с планировщиком заданий Windows
- **Автоматическая настройка** - PowerShell скрипт для быстрой настройки
- **Batch файлы** - Готовые bat-файлы для запуска

#### Новые файлы:
- `headless_parser.py` - Headless версия парсера
- `auto_parser.bat` - Batch файл для автозапуска
- `setup_task_scheduler.ps1` - PowerShell скрипт настройки
- `АВТОЗАПУСК_ИНСТРУКЦИЯ.md` - Подробная инструкция

#### Расширенные возможности:
- **Аргументы командной строки** - Гибкая настройка запуска
- **Автопоиск файлов** - Автоматический поиск Excel файлов
- **Улучшенное логирование** - Отдельные логи для headless режима
- **Проверка времени** - Запуск только в определенное время
- **Тестовый режим** - Dry-run для проверки без парсинга

### 🔧 Улучшения

#### Документация:
- Обновлен README.md с новыми возможностями
- Добавлена структура проекта
- Расширен раздел устранения неполадок
- Добавлены примеры использования автоматизации

#### Стабильность:
- Улучшена обработка ошибок в headless режиме
- Добавлены проверки существования файлов
- Расширено логирование для диагностики

### 📖 Использование

#### Быстрая настройка автозапуска:
```powershell
# В PowerShell от администратора
.\setup_task_scheduler.ps1
```

#### Headless режим:
```bash
# Обычный запуск
python headless_parser.py

# С подробным выводом
python headless_parser.py -v

# Тестовый режим
python headless_parser.py --dry-run
```

#### Проверка автоматизации:
```powershell
# Статус задачи
Get-ScheduledTask -TaskName "Instagram_Reels_Parser_Daily"

# Тестовый запуск
Start-ScheduledTask -TaskName "Instagram_Reels_Parser_Daily"
```

---

## [1.0.0] - 2024-01-20

### 🎉 Первоначальный релиз

#### Основные возможности:
- **GUI приложение** на Tkinter
- **Парсинг Instagram Reels** через HikerAPI
- **Excel интеграция** с группировкой по дням
- **Автоматические бэкапы** перед изменениями
- **Конфигурационный файл** для настроек
- **Подробное логирование** операций

#### Файлы первоначального релиза:
- `instagram_reels_parser.py` - Основное GUI приложение
- `config.ini` - Конфигурационный файл
- `requirements.txt` - Зависимости Python
- `create_test_reels.py` - Генератор тестовых файлов
- `README.md` - Основная документация

#### Поддерживаемые форматы:
- **Excel файлы** (.xlsx, .xls)
- **Instagram Reels** URL форматы
- **Группировка по дням** (колонки 1-31)

---

## Планы на будущее

### v2.1.0 (планируется)
- [ ] Поддержка Linux и macOS автоматизации
- [ ] Уведомления в Telegram
- [ ] Веб-интерфейс для мониторинга

### v2.2.0 (планируется)
- [ ] Поддержка TikTok и YouTube Shorts
- [ ] Многопоточный парсинг
- [ ] REST API

### v3.0.0 (планируется)
- [ ] Полнофункциональный дашборд
- [ ] Анализ трендов и графики
- [ ] Интеграция с Google Sheets 