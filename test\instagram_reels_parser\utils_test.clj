(ns instagram-reels-parser.utils-test
  (:require [clojure.test :refer :all]
            [instagram-reels-parser.utils :as utils]))

(deftest test-extract-shortcode
  (testing "Extract shortcode from various URL formats"
    (is (= "ABC123def456" (utils/extract-shortcode "https://www.instagram.com/reel/ABC123def456/")))
    (is (= "ABC123def456" (utils/extract-shortcode "https://instagram.com/reel/ABC123def456")))
    (is (= "ABC123def456" (utils/extract-shortcode "https://www.instagram.com/reels/ABC123def456/")))
    (is (= "ABC123def456" (utils/extract-shortcode "https://www.instagram.com/p/ABC123def456/")))
    (is (nil? (utils/extract-shortcode "https://www.example.com/invalid")))
    (is (nil? (utils/extract-shortcode "")))
    (is (nil? (utils/extract-shortcode nil)))))

(deftest test-validate-shortcode
  (testing "Validate shortcode format"
    (is (utils/validate-shortcode "ABC123def456"))
    (is (utils/validate-shortcode "A1B2C3D4E5F6"))
    (is (not (utils/validate-shortcode "short")))  ; too short
    (is (not (utils/validate-shortcode "toolongshortcode123")))  ; too long
    (is (not (utils/validate-shortcode "invalid@chars")))  ; invalid chars
    (is (not (utils/validate-shortcode nil)))
    (is (not (utils/validate-shortcode "")))))

(deftest test-validate-instagram-url
  (testing "Validate Instagram URL and extract shortcode"
    (is (= "ABC123def456" (utils/validate-instagram-url "https://www.instagram.com/reel/ABC123def456/")))
    (is (nil? (utils/validate-instagram-url "https://www.instagram.com/reel/short/")))
    (is (nil? (utils/validate-instagram-url "https://www.example.com/invalid")))))

(deftest test-format-number
  (testing "Format numbers with thousands separators"
    (is (= "1,000" (utils/format-number 1000)))
    (is (= "1,234,567" (utils/format-number 1234567)))
    (is (= "0" (utils/format-number 0)))
    (is (= "test" (utils/format-number "test")))))

(deftest test-safe-parse-int
  (testing "Safe integer parsing"
    (is (= 123 (utils/safe-parse-int "123")))
    (is (= 0 (utils/safe-parse-int "invalid")))
    (is (= 42 (utils/safe-parse-int "invalid" 42)))
    (is (= 123 (utils/safe-parse-int 123)))
    (is (= 0 (utils/safe-parse-int nil)))))

(deftest test-safe-parse-double
  (testing "Safe double parsing"
    (is (= 123.45 (utils/safe-parse-double "123.45")))
    (is (= 0.0 (utils/safe-parse-double "invalid")))
    (is (= 42.0 (utils/safe-parse-double "invalid" 42.0)))
    (is (= 123.0 (utils/safe-parse-double 123)))
    (is (= 0.0 (utils/safe-parse-double nil)))))

(deftest test-sanitize-filename
  (testing "Sanitize filename"
    (is (= "test_file.txt" (utils/sanitize-filename "test file.txt")))
    (is (= "test_file_name.txt" (utils/sanitize-filename "test<>file|name.txt")))
    (is (= "normal_filename.txt" (utils/sanitize-filename "normal_filename.txt")))
    (is (nil? (utils/sanitize-filename nil)))))

(deftest test-truncate-string
  (testing "Truncate string"
    (is (= "hello..." (utils/truncate-string "hello world" 8)))
    (is (= "hello world" (utils/truncate-string "hello world" 20)))
    (is (= "hello!!!" (utils/truncate-string "hello world" 8 "!!!")))
    (is (nil? (utils/truncate-string nil 10)))))
