@echo off
REM Instagram Reels Parser - Windows Launcher
REM This script launches the Instagram Reels Parser with GUI

echo Starting Instagram Reels Parser...

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java 11 or higher
    pause
    exit /b 1
)

REM Check if JAR file exists
if not exist "target\uberjar\instagram-reels-parser-1.0.0-standalone.jar" (
    echo JAR file not found. Building project...
    call lein uberjar
    if %errorlevel% neq 0 (
        echo Error: Failed to build project
        pause
        exit /b 1
    )
)

REM Launch the application
java -jar target\uberjar\instagram-reels-parser-1.0.0-standalone.jar %*

if %errorlevel% neq 0 (
    echo Application exited with error code %errorlevel%
    pause
)
