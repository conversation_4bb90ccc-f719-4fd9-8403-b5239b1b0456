(ns instagram-reels-parser.core-test
  (:require [clojure.test :refer :all]
            [instagram-reels-parser.core :as core]))

(deftest test-validate-args
  (testing "Help option"
    (let [result (core/validate-args ["--help"])]
      (is (:ok? result))
      (is (:exit-message result))))
  
  (testing "GUI mode (default)"
    (let [result (core/validate-args [])]
      (is (= :gui (:action result)))))
  
  (testing "CLI mode with file"
    (let [result (core/validate-args ["--cli" "--file" "test.xlsx"])]
      (is (= :cli (:action result)))))
  
  (testing "CLI mode without file"
    (let [result (core/validate-args ["--cli"])]
      (is (:exit-message result))))
  
  (testing "Schedule mode"
    (let [result (core/validate-args ["--schedule" "--file" "test.xlsx"])]
      (is (= :schedule (:action result))))))

(deftest test-error-msg
  (testing "Error message formatting"
    (let [errors ["Error 1" "Error 2"]
          result (core/error-msg errors)]
      (is (clojure.string/includes? result "Error 1"))
      (is (clojure.string/includes? result "Error 2")))))

(deftest test-usage
  (testing "Usage message"
    (let [result (core/usage "test options")]
      (is (clojure.string/includes? result "Instagram Reels Parser"))
      (is (clojure.string/includes? result "test options")))))
