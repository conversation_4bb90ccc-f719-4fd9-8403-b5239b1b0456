# 🔑 Настройка API ключа HikerAPI

## Быстрая настройка

### 1. Получение API ключа
1. Зайдите на [hikerapi.com](https://hikerapi.com)
2. Зарегистрируйтесь или войдите в аккаунт
3. Перейдите в раздел "API Keys" или "Dashboard"
4. Скопируйте ваш API ключ

### 2. Настройка в программе
1. **Запустите программу**: `python instagram_reels_parser.py`
2. **Откройте настройки**: Нажмите кнопку "Настройки"
3. **Вставьте API ключ**:
   - Поле скрыто звездочками для безопасности
   - Поставьте галочку "Показать" чтобы видеть что вводите
   - Вставьте ваш ключ (Ctrl+V)
4. **Протестируйте**: Нажмите кнопку "Тест API"
5. **Сохраните**: Нажмите "Сохранить"

### 3. Альтернативный способ (через файл)
Если GUI не работает, отредактируйте файл `config.ini`:
```ini
[API]
hiker_api_key = ваш_реальный_ключ_сюда
```

## 🔧 Решение проблем

### Проблема: "Не могу вставить ключ"
**Решение:**
1. Убедитесь, что поставили галочку "Показать"
2. Выделите всё в поле (Ctrl+A) перед вставкой
3. Попробуйте правой кнопкой мыши → Вставить
4. Или отредактируйте `config.ini` напрямую

### Проблема: "API ключ не работает"
**Решение:**
1. Проверьте правильность ключа
2. Убедитесь, что подписка активна
3. Проверьте баланс на hikerapi.com
4. Используйте кнопку "Тест API" для проверки

### Проблема: "Превышен лимит"
**Решение:**
1. Увеличьте задержку между запросами в настройках
2. Проверьте лимиты на hikerapi.com
3. Подождите до обновления лимитов

## ✅ Проверка настройки

После настройки убедитесь что:
- [ ] API ключ вставлен в настройки
- [ ] Кнопка "Тест API" показывает успех
- [ ] Excel файл с Reels выбран
- [ ] Кнопка "Запустить парсинг" активна

**Готово к использованию!** 🎉 