(ns instagram-reels-parser.config
  "Configuration management for Instagram Reels Parser"
  (:require [clojure.java.io :as io]
            [clojure.string :as str]
            [clojure.tools.logging :as log])
  (:import [java.io File FileInputStream]
           [java.util Properties]))

(def ^:dynamic *config* (atom {}))

(def default-config
  {:api {:access-key "YOUR_ACCESS_KEY_HERE"
         :hiker-api-key "YOUR_ACCESS_KEY_HERE"
         :base-url "https://api.hikerapi.com/v2"
         :debug false}
   :settings {:daily-run-time "09:00"
              :request-delay 2
              :max-retries 3
              :request-timeout 30}
   :files {:url-column "Ссылка на Reels"
           :account-column "Account_Name"
           :save-mode "same_file"}
   :gui {:last-file ""}})

(defn parse-ini-file
  "Parse INI file and return a map"
  [file-path]
  (try
    (let [props (Properties.)
          file (File. file-path)]
      (when (.exists file)
        (with-open [input (FileInputStream. file)]
          (.load props input))
        (let [config (atom {})]
          (doseq [[key value] props]
            (let [key-str (str key)
                  value-str (str value)]
              (cond
                ;; API section
                (str/starts-with? key-str "api.")
                (let [api-key (keyword (subs key-str 4))]
                  (swap! config assoc-in [:api api-key] value-str))
                
                ;; Settings section
                (str/starts-with? key-str "settings.")
                (let [setting-key (keyword (subs key-str 9))
                      parsed-value (cond
                                     (re-matches #"\d+" value-str) (Integer/parseInt value-str)
                                     (re-matches #"\d+\.\d+" value-str) (Double/parseDouble value-str)
                                     (= "true" (str/lower-case value-str)) true
                                     (= "false" (str/lower-case value-str)) false
                                     :else value-str)]
                  (swap! config assoc-in [:settings setting-key] parsed-value))
                
                ;; Files section
                (str/starts-with? key-str "files.")
                (let [file-key (keyword (subs key-str 6))]
                  (swap! config assoc-in [:files file-key] value-str))
                
                ;; GUI section
                (str/starts-with? key-str "gui.")
                (let [gui-key (keyword (subs key-str 4))]
                  (swap! config assoc-in [:gui gui-key] value-str))
                
                ;; Legacy format support
                :else
                (let [section-key (if (str/includes? key-str ".")
                                    (let [parts (str/split key-str #"\." 2)]
                                      [(keyword (str/lower-case (first parts)))
                                       (keyword (str/replace (second parts) "_" "-"))])
                                    [:general (keyword (str/replace key-str "_" "-"))])]
                  (swap! config assoc-in section-key value-str)))))
          @config)))
    (catch Exception e
      (log/error e "Failed to parse INI file:" file-path)
      {})))

(defn create-default-config-file!
  "Create default configuration file"
  [file-path]
  (try
    (let [config-content (str "[API]\n"
                              "access_key = YOUR_ACCESS_KEY_HERE\n"
                              "hiker_api_key = YOUR_ACCESS_KEY_HERE\n"
                              "base_url = https://api.hikerapi.com/v2\n"
                              "debug = false\n\n"
                              "[SETTINGS]\n"
                              "daily_run_time = 09:00\n"
                              "request_delay = 2\n"
                              "max_retries = 3\n"
                              "request_timeout = 30\n\n"
                              "[FILES]\n"
                              "url_column = Ссылка на Reels\n"
                              "account_column = Account_Name\n"
                              "save_mode = same_file\n\n"
                              "[GUI]\n"
                              "last_file = \n")]
      (spit file-path config-content :encoding "UTF-8")
      (log/info "Created default configuration file:" file-path))
    (catch Exception e
      (log/error e "Failed to create default configuration file:" file-path))))

(defn load-config!
  "Load configuration from file or create default"
  ([file-path]
   (let [config-file (File. file-path)]
     (when-not (.exists config-file)
       (log/info "Configuration file not found, creating default:" file-path)
       (create-default-config-file! file-path))
     
     (let [loaded-config (parse-ini-file file-path)
           merged-config (merge-with merge default-config loaded-config)]
       (reset! *config* merged-config)
       (log/info "Configuration loaded successfully")
       merged-config)))
  ([]
   (load-config! "config.ini")))

(defn get-config
  "Get configuration value by path"
  ([path]
   (get-in @*config* path))
  ([path default]
   (get-in @*config* path default)))

(defn set-config!
  "Set configuration value by path"
  [path value]
  (swap! *config* assoc-in path value))

(defn save-config!
  "Save current configuration to file"
  [file-path]
  (try
    (let [config @*config*
          content (str "[API]\n"
                       "access_key = " (get-in config [:api :access-key] "") "\n"
                       "hiker_api_key = " (get-in config [:api :hiker-api-key] "") "\n"
                       "base_url = " (get-in config [:api :base-url] "") "\n"
                       "debug = " (get-in config [:api :debug] false) "\n\n"
                       "[SETTINGS]\n"
                       "daily_run_time = " (get-in config [:settings :daily-run-time] "") "\n"
                       "request_delay = " (get-in config [:settings :request-delay] 2) "\n"
                       "max_retries = " (get-in config [:settings :max-retries] 3) "\n"
                       "request_timeout = " (get-in config [:settings :request-timeout] 30) "\n\n"
                       "[FILES]\n"
                       "url_column = " (get-in config [:files :url-column] "") "\n"
                       "account_column = " (get-in config [:files :account-column] "") "\n"
                       "save_mode = " (get-in config [:files :save-mode] "") "\n\n"
                       "[GUI]\n"
                       "last_file = " (get-in config [:gui :last-file] "") "\n")]
      (spit file-path content :encoding "UTF-8")
      (log/info "Configuration saved to:" file-path)
      true)
    (catch Exception e
      (log/error e "Failed to save configuration to:" file-path)
      false)))
