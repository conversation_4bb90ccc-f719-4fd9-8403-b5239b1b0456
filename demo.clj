#!/usr/bin/env clojure

;; Демонстрационная версия Instagram Reels Parser
;; Показывает основную функциональность без полной сборки

(ns demo
  (:require [clojure.string :as str]
            [clojure.java.io :as io]))

(println "🚀 Instagram Reels Parser - Clojure Edition Demo")
(println "=" (apply str (repeat 50 "=")))

;; Демонстрация парсинга URL
(defn extract-shortcode [url]
  "Извлекает shortcode из Instagram URL"
  (when url
    (let [patterns [#"instagram\.com/reel/([A-Za-z0-9_-]+)"
                    #"instagram\.com/reels/([A-Za-z0-9_-]+)"
                    #"instagram\.com/p/([A-Za-z0-9_-]+)"]]
      (some #(second (re-find % url)) patterns))))

;; Демонстрация валидации
(defn validate-shortcode [shortcode]
  "Проверяет корректность shortcode"
  (and shortcode
       (string? shortcode)
       (>= (count shortcode) 8)
       (<= (count shortcode) 15)
       (re-matches #"[A-Za-z0-9_-]+" shortcode)))

;; Демонстрация конфигурации
(defn load-demo-config []
  "Загружает демонстрационную конфигурацию"
  {:api {:access-key "DEMO_KEY"
         :base-url "https://api.hikerapi.com/v2"
         :debug true}
   :settings {:daily-run-time "09:00"
              :request-delay 2
              :max-retries 3}})

;; Демонстрация обработки данных
(defn format-number [n]
  "Форматирует число с разделителями тысяч"
  (if (number? n)
    (let [s (str n)]
      (str/join "," (reverse (map str/join (partition-all 3 (reverse s))))))
    (str n)))

;; Тестовые данные
(def test-urls
  ["https://www.instagram.com/reel/ABC123def456/"
   "https://www.instagram.com/reel/XYZ789ghi012/"
   "https://www.instagram.com/p/QWE345rty678/"
   "https://invalid-url.com/test"
   ""])

(println "\n📋 Демонстрация функций:")
(println)

;; Тест парсинга URL
(println "🔗 Парсинг Instagram URL:")
(doseq [url test-urls]
  (let [shortcode (extract-shortcode url)]
    (printf "  %-45s -> %s%n" 
            (if (empty? url) "(пустая строка)" url)
            (if shortcode shortcode "❌ не найден"))))

(println)

;; Тест валидации shortcode
(println "✅ Валидация shortcode:")
(let [test-codes ["ABC123def456" "XYZ789" "toolongshortcode123" "invalid@chars" ""]]
  (doseq [code test-codes]
    (printf "  %-20s -> %s%n" 
            (if (empty? code) "(пустой)" code)
            (if (validate-shortcode code) "✅ валидный" "❌ невалидный"))))

(println)

;; Тест форматирования чисел
(println "📊 Форматирование чисел:")
(let [test-numbers [1000 1234567 0 "test"]]
  (doseq [num test-numbers]
    (printf "  %-10s -> %s%n" num (format-number num))))

(println)

;; Демонстрация конфигурации
(println "⚙️ Конфигурация:")
(let [config (load-demo-config)]
  (printf "  API ключ: %s%n" (get-in config [:api :access-key]))
  (printf "  Базовый URL: %s%n" (get-in config [:api :base-url]))
  (printf "  Время запуска: %s%n" (get-in config [:settings :daily-run-time]))
  (printf "  Задержка запросов: %s сек%n" (get-in config [:settings :request-delay])))

(println)

;; Проверка файлов проекта
(println "📁 Структура проекта:")
(let [files ["project.clj" "src/instagram_reels_parser/core.clj" 
             "src/instagram_reels_parser/gui.clj" "config.ini"]]
  (doseq [file files]
    (printf "  %-40s -> %s%n" file 
            (if (.exists (io/file file)) "✅ существует" "❌ не найден"))))

(println)
(println "🎯 Основные модули созданы:")
(println "  ✅ core.clj - главный модуль с CLI")
(println "  ✅ config.clj - управление конфигурацией") 
(println "  ✅ api.clj - HTTP клиент для HikerAPI")
(println "  ✅ utils.clj - утилиты парсинга")
(println "  ✅ excel.clj - работа с Excel файлами")
(println "  ✅ parser.clj - логика парсинга")
(println "  ✅ gui.clj - графический интерфейс")
(println "  ✅ scheduler.clj - планировщик задач")

(println)
(println "🚀 Для полного запуска:")
(println "  1. Установите Leiningen (см. INSTALL_LEININGEN.md)")
(println "  2. Выполните: build.bat")
(println "  3. Настройте config.ini с вашим API ключом")
(println "  4. Запустите: run.bat")

(println)
(println "✅ Демонстрация завершена!")
(println "📖 См. README_CLOJURE.md для подробной документации")
