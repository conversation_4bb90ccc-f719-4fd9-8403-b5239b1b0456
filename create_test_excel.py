#!/usr/bin/env python3
"""
Создание тестового Excel файла для Instagram Reels Parser
"""

import pandas as pd
import os

def create_test_excel():
    """Создает тестовый Excel файл с примерами данных"""
    
    # Создаем тестовые данные
    test_data = {
        'Ссылка на Reels': [
            'https://www.instagram.com/reel/ABC123def456/',
            'https://www.instagram.com/reel/XYZ789ghi012/',
            'https://www.instagram.com/reel/QWE345rty678/',
            'https://www.instagram.com/reel/ASD901zxc234/',
            'https://www.instagram.com/reel/FGH567jkl890/'
        ],
        'Account_Name': [
            '@test_account_1',
            '@test_account_2', 
            '@test_account_3',
            '@test_account_4',
            '@test_account_5'
        ]
    }
    
    # Добавляем колонки для дней месяца (1-31)
    for day in range(1, 32):
        test_data[str(day)] = ['' for _ in range(5)]
    
    # Создаем DataFrame
    df = pd.DataFrame(test_data)
    
    # Сохраняем в Excel файл
    output_file = 'test_data.xlsx'
    df.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"✅ Тестовый Excel файл создан: {output_file}")
    print(f"📊 Строк данных: {len(df)}")
    print(f"📋 Колонок: {len(df.columns)}")
    
    # Показываем первые несколько строк
    print("\n📝 Пример данных:")
    print(df[['Ссылка на Reels', 'Account_Name', '1', '2', '3']].head())
    
    return output_file

if __name__ == "__main__":
    try:
        create_test_excel()
    except ImportError:
        print("❌ Ошибка: pandas не установлен")
        print("Установите pandas: pip install pandas openpyxl")
    except Exception as e:
        print(f"❌ Ошибка при создании файла: {e}")
