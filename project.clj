(defproject instagram-reels-parser "1.0.0"
  :description "Instagram Reels Parser - Clojure version"
  :url "https://github.com/your-username/instagram-reels-parser"
  :license {:name "MIT License"
            :url "https://opensource.org/licenses/MIT"}
  
  :dependencies [[org.clojure/clojure "1.11.1"]
                 [clj-http "3.12.3"]                    ; HTTP client
                 [cheshire "5.11.0"]                    ; JSON parsing
                 [org.clojure/data.csv "1.0.1"]         ; CSV handling
                 [org.apache.poi/poi "5.2.4"]           ; Excel reading/writing
                 [org.apache.poi/poi-ooxml "5.2.4"]     ; Excel OOXML support
                 [org.apache.poi/poi-ooxml-schemas "4.1.2"] ; Excel schemas
                 [seesaw "1.5.0"]                       ; GUI framework
                 [org.clojure/tools.logging "1.2.4"]    ; Logging
                 [ch.qos.logback/logback-classic "1.4.11"] ; Logback implementation
                 [clj-time "0.15.2"]                    ; Date/time handling
                 [org.clojure/core.async "1.6.673"]     ; Async programming
                 [overtone/at-at "1.2.0"]               ; Scheduling
                 [org.clojure/tools.cli "1.0.219"]      ; Command line parsing
                 [me.raynes/fs "1.4.6"]]                ; File system utilities
  
  :main ^:skip-aot instagram-reels-parser.core
  :target-path "target/%s"
  :profiles {:uberjar {:aot :all
                       :jvm-opts ["-Dclojure.compiler.direct-linking=true"]}
             :dev {:dependencies [[org.clojure/test.check "1.1.1"]]}}
  
  :jvm-opts ["-Xmx2g" "-Dfile.encoding=UTF-8"]
  
  :resource-paths ["resources"]
  
  :plugins [[lein-ancient "1.0.0-RC3"]
            [lein-cljfmt "0.9.0"]])
