@echo off
REM Instagram Reels Parser - Manual Build Script (without <PERSON><PERSON><PERSON>)
REM This script manually compiles the Clojure code using Java

echo Building Instagram Reels Parser manually...

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java 11 or higher
    pause
    exit /b 1
)

REM Create directories
if not exist "target" mkdir target
if not exist "target\classes" mkdir target\classes
if not exist "target\lib" mkdir target\lib

echo Downloading Clojure dependencies...

REM Download Clojure JAR
if not exist "target\lib\clojure-1.11.1.jar" (
    echo Downloading Clojure 1.11.1...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/clojure/clojure/1.11.1/clojure-1.11.1.jar' -OutFile 'target\lib\clojure-1.11.1.jar'"
)

REM Download other dependencies (simplified set)
if not exist "target\lib\clj-http-3.12.3.jar" (
    echo Downloading clj-http...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/clj-http/clj-http/3.12.3/clj-http-3.12.3.jar' -OutFile 'target\lib\clj-http-3.12.3.jar'"
)

if not exist "target\lib\cheshire-5.11.0.jar" (
    echo Downloading cheshire...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/cheshire/cheshire/5.11.0/cheshire-5.11.0.jar' -OutFile 'target\lib\cheshire-5.11.0.jar'"
)

echo.
echo Manual compilation requires significant dependency management.
echo.
echo RECOMMENDED APPROACH:
echo 1. Install Leiningen from https://leiningen.org/
echo 2. Run build.bat instead
echo.
echo For immediate testing, you can run the code directly:
echo   java -cp "src;target\lib\*" clojure.main -m instagram-reels-parser.core
echo.
echo See DEPLOYMENT.md for complete installation instructions.

pause
